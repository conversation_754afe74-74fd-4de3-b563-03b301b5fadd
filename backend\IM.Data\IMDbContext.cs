using Microsoft.EntityFrameworkCore;
using IM.Models;

namespace IM.Data
{
    public class IMDbContext : DbContext
    {
        public IMDbContext(DbContextOptions<IMDbContext> options) : base(options)
        {
        }
        
        public DbSet<User> Users { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<ChatRoom> ChatRooms { get; set; }
        public DbSet<ChatRoomMember> ChatRoomMembers { get; set; }
        public DbSet<Friendship> Friendships { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // User configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.LastSeen).HasDefaultValueSql("GETUTCDATE()");
            });
            
            // Message configuration
            modelBuilder.Entity<Message>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SentAt).HasDefaultValueSql("GETUTCDATE()");
                
                // Sender relationship
                entity.HasOne(e => e.Sender)
                    .WithMany(u => u.SentMessages)
                    .HasForeignKey(e => e.SenderId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // Receiver relationship (for private messages)
                entity.HasOne(e => e.Receiver)
                    .WithMany(u => u.ReceivedMessages)
                    .HasForeignKey(e => e.ReceiverId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // ChatRoom relationship (for group messages)
                entity.HasOne(e => e.ChatRoom)
                    .WithMany(c => c.Messages)
                    .HasForeignKey(e => e.ChatRoomId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // ChatRoom configuration
            modelBuilder.Entity<ChatRoom>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Owner)
                    .WithMany(u => u.OwnedChatRooms)
                    .HasForeignKey(e => e.OwnerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // ChatRoomMember configuration
            modelBuilder.Entity<ChatRoomMember>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.ChatRoomId, e.UserId }).IsUnique();
                entity.Property(e => e.JoinedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.ChatRoom)
                    .WithMany(c => c.Members)
                    .HasForeignKey(e => e.ChatRoomId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.User)
                    .WithMany(u => u.ChatRoomMemberships)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Friendship configuration
            modelBuilder.Entity<Friendship>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.RequesterId, e.AddresseeId }).IsUnique();
                entity.Property(e => e.RequestedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Requester)
                    .WithMany(u => u.InitiatedFriendships)
                    .HasForeignKey(e => e.RequesterId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Addressee)
                    .WithMany(u => u.ReceivedFriendships)
                    .HasForeignKey(e => e.AddresseeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
