using Microsoft.Extensions.Logging;
using Railway.TicketSystem.Models.DTOs;
using Railway.TicketSystem.Models.Entities;
using Railway.TicketSystem.Data.Repositories;
using Railway.TicketSystem.Cache;
using System.Diagnostics;

namespace Railway.TicketSystem.Core.Services
{
    public class TrainService : ITrainService
    {
        private readonly ITrainRepository _trainRepository;
        private readonly IInventoryRepository _inventoryRepository;
        private readonly ICacheService _cacheService;
        private readonly ILogger<TrainService> _logger;
        
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);
        private readonly string[] _popularRoutes = { "北京-上海", "北京-广州", "上海-深圳", "北京-西安", "上海-杭州" };

        public TrainService(
            ITrainRepository trainRepository,
            IInventoryRepository inventoryRepository,
            ICacheService cacheService,
            ILogger<TrainService> logger)
        {
            _trainRepository = trainRepository;
            _inventoryRepository = inventoryRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<TrainQueryResponse> SearchTrainsAsync(TrainQueryRequest request)
        {
            var stopwatch = Stopwatch.StartNew();
            var queryId = GenerateQueryId(request);
            
            _logger.LogInformation("开始查询列车: {FromStation} -> {ToStation}, 日期: {TravelDate}", 
                request.FromStation, request.ToStation, request.TravelDate);

            // 尝试从缓存获取
            var cacheKey = $"train:query:{queryId}";
            var cachedResult = await _cacheService.GetAsync<TrainQueryResponse>(cacheKey);
            
            if (cachedResult != null)
            {
                _logger.LogInformation("从缓存返回查询结果: {QueryId}", queryId);
                cachedResult.FromCache = true;
                cachedResult.ResponseTime = stopwatch.Elapsed;
                return cachedResult;
            }

            // 从数据库查询
            var dbStopwatch = Stopwatch.StartNew();
            var trains = await _trainRepository.SearchTrainsAsync(
                request.FromStation, 
                request.ToStation, 
                request.TravelDate);
            dbStopwatch.Stop();

            // 获取库存信息
            var trainInfos = new List<TrainInfo>();
            foreach (var train in trains)
            {
                var trainInfo = await BuildTrainInfoAsync(train, request.TravelDate, request.FromStation, request.ToStation);
                if (trainInfo != null && (!request.OnlyAvailable || trainInfo.HasTicket))
                {
                    trainInfos.Add(trainInfo);
                }
            }

            // 分页处理
            var totalCount = trainInfos.Count;
            var pagedTrains = trainInfos
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var response = new TrainQueryResponse
            {
                Trains = pagedTrains,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                HasNextPage = request.PageNumber * request.PageSize < totalCount,
                HasPreviousPage = request.PageNumber > 1,
                QueryId = queryId,
                QueryTime = DateTime.UtcNow,
                FromCache = false,
                ResponseTime = stopwatch.Elapsed
            };

            // 缓存结果
            await _cacheService.SetAsync(cacheKey, response, _cacheExpiration);
            
            _logger.LogInformation("查询完成: {QueryId}, 耗时: {ElapsedMs}ms, 结果数: {Count}", 
                queryId, stopwatch.ElapsedMilliseconds, totalCount);

            return response;
        }

        public async Task<TrainInfo?> GetTrainDetailAsync(int trainId, DateTime travelDate)
        {
            var cacheKey = $"train:detail:{trainId}:{travelDate:yyyyMMdd}";
            var cachedResult = await _cacheService.GetAsync<TrainInfo>(cacheKey);
            
            if (cachedResult != null)
            {
                return cachedResult;
            }

            var train = await _trainRepository.GetTrainByIdAsync(trainId);
            if (train == null) return null;

            var trainInfo = await BuildTrainInfoAsync(train, travelDate, 
                train.StartStation.StationName, train.EndStation.StationName);

            if (trainInfo != null)
            {
                await _cacheService.SetAsync(cacheKey, trainInfo, _cacheExpiration);
            }

            return trainInfo;
        }

        public async Task<List<string>> GetPopularRoutesAsync()
        {
            return _popularRoutes.ToList();
        }

        public async Task WarmupCacheAsync(DateTime startDate, int days = 7)
        {
            _logger.LogInformation("开始缓存预热: {StartDate}, 天数: {Days}", startDate, days);

            var tasks = new List<Task>();
            
            foreach (var route in _popularRoutes)
            {
                var parts = route.Split('-');
                if (parts.Length == 2)
                {
                    for (int i = 0; i < days; i++)
                    {
                        var date = startDate.AddDays(i);
                        var request = new TrainQueryRequest
                        {
                            FromStation = parts[0],
                            ToStation = parts[1],
                            TravelDate = date,
                            PageSize = 50
                        };
                        
                        tasks.Add(SearchTrainsAsync(request));
                    }
                }
            }

            await Task.WhenAll(tasks);
            _logger.LogInformation("缓存预热完成");
        }

        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            var cacheStats = await _cacheService.GetStatisticsAsync();
            
            return new PerformanceMetrics
            {
                CacheHit = cacheStats.OverallHitRate > 0,
                CacheLevel = cacheStats.L1HitRate > 0 ? "L1" : (cacheStats.L2HitRate > 0 ? "L2" : "None"),
                Timestamp = DateTime.UtcNow
            };
        }

        public async Task RefreshTrainCacheAsync(int trainId)
        {
            // 清除相关缓存
            await _cacheService.RemoveByPatternAsync($"train:*:{trainId}:*");
            _logger.LogInformation("已清除列车缓存: {TrainId}", trainId);
        }

        private async Task<TrainInfo?> BuildTrainInfoAsync(Train train, DateTime travelDate, string fromStation, string toStation)
        {
            try
            {
                // 获取库存信息
                var inventories = await _inventoryRepository.GetInventoryAsync(train.Id, travelDate);
                
                var seatAvailabilities = inventories.Select(inv => new SeatAvailability
                {
                    SeatTypeId = inv.SeatTypeId,
                    SeatTypeName = inv.SeatType.TypeName,
                    SeatTypeCode = inv.SeatType.TypeCode,
                    TotalSeats = inv.TotalSeats,
                    AvailableSeats = inv.AvailableSeats,
                    BookedSeats = inv.BookedSeats,
                    OccupancyRate = inv.OccupancyRate,
                    AvailabilityStatus = GetAvailabilityStatus(inv.AvailableSeats, inv.TotalSeats)
                }).ToList();

                return new TrainInfo
                {
                    TrainId = train.Id,
                    TrainNumber = train.TrainNumber,
                    TrainType = train.TrainType,
                    FromStation = fromStation,
                    ToStation = toStation,
                    DepartureTime = train.DepartureTime,
                    ArrivalTime = train.ArrivalTime,
                    Duration = train.Duration,
                    Distance = train.TotalDistance,
                    SeatAvailabilities = seatAvailabilities,
                    HasTicket = seatAvailabilities.Any(sa => sa.AvailableSeats > 0),
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建列车信息失败: {TrainId}", train.Id);
                return null;
            }
        }

        private string GetAvailabilityStatus(int availableSeats, int totalSeats)
        {
            if (availableSeats == 0) return "无票";
            
            var rate = (double)availableSeats / totalSeats;
            return rate > 0.3 ? "充足" : (rate > 0.1 ? "紧张" : "少量");
        }

        private string GenerateQueryId(TrainQueryRequest request)
        {
            return $"{request.FromStation}_{request.ToStation}_{request.TravelDate:yyyyMMdd}_{request.TrainType ?? "ALL"}";
        }
    }
}
