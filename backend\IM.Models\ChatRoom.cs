using System.ComponentModel.DataAnnotations;

namespace IM.Models
{
    public class ChatRoom
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public ChatRoomType Type { get; set; } = ChatRoomType.Group;
        
        public int OwnerId { get; set; }
        public virtual User Owner { get; set; } = null!;
        
        [StringLength(500)]
        public string? Avatar { get; set; }
        
        public bool IsPrivate { get; set; }
        
        public int MaxMembers { get; set; } = 100;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        public bool IsDeleted { get; set; }
        
        // Navigation properties
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
        public virtual ICollection<ChatRoomMember> Members { get; set; } = new List<ChatRoomMember>();
    }
    
    public enum ChatRoomType
    {
        Private = 0,  // 1对1私聊
        Group = 1,    // 群聊
        Channel = 2   // 频道
    }
}
