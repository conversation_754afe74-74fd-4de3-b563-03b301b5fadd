using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 列车实体
    /// </summary>
    public class Train
    {
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string TrainType { get; set; } = string.Empty; // G-高铁, D-动车, T-特快, K-快速, 普通

        public int StartStationId { get; set; }
        public virtual Station StartStation { get; set; } = null!;

        public int EndStationId { get; set; }
        public virtual Station EndStation { get; set; } = null!;

        public TimeSpan DepartureTime { get; set; }

        public TimeSpan ArrivalTime { get; set; }

        public TimeSpan Duration { get; set; }

        public int TotalDistance { get; set; } // 总里程（公里）

        public TrainStatus Status { get; set; } = TrainStatus.Active;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<TrainRoute> TrainRoutes { get; set; } = new List<TrainRoute>();
        public virtual ICollection<TrainSeat> TrainSeats { get; set; } = new List<TrainSeat>();
        public virtual ICollection<TicketPrice> TicketPrices { get; set; } = new List<TicketPrice>();
        public virtual ICollection<SeatInventory> SeatInventories { get; set; } = new List<SeatInventory>();
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
    }

    public enum TrainStatus
    {
        Active = 1,     // 正常运行
        Suspended = 2,  // 暂停运行
        Cancelled = 3   // 停运
    }

    /// <summary>
    /// 座位类型实体
    /// </summary>
    public class SeatType
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty; // 商务座、一等座、二等座、硬卧、软卧等

        [Required]
        [StringLength(10)]
        public string TypeCode { get; set; } = string.Empty; // SW, YD, ED, YW, RW等

        public decimal BasePrice { get; set; } // 基础价格（每公里）

        public decimal PriceMultiplier { get; set; } = 1.0m; // 价格倍数

        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public int SortOrder { get; set; } // 排序

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<TrainSeat> TrainSeats { get; set; } = new List<TrainSeat>();
        public virtual ICollection<TicketPrice> TicketPrices { get; set; } = new List<TicketPrice>();
        public virtual ICollection<SeatInventory> SeatInventories { get; set; } = new List<SeatInventory>();
    }

    /// <summary>
    /// 列车路线实体（列车经停站点）
    /// </summary>
    public class TrainRoute
    {
        public int Id { get; set; }

        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;

        public int StationId { get; set; }
        public virtual Station Station { get; set; } = null!;

        public int StopOrder { get; set; } // 停靠顺序

        public TimeSpan? ArrivalTime { get; set; } // 到达时间（起始站为null）

        public TimeSpan? DepartureTime { get; set; } // 出发时间（终点站为null）

        public int StayMinutes { get; set; } // 停留时间（分钟）

        public int DistanceFromStart { get; set; } // 距离起始站的里程

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 列车座位实体
    /// </summary>
    public class TrainSeat
    {
        public int Id { get; set; }

        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;

        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;

        public int CarriageNumber { get; set; } // 车厢号

        [Required]
        [StringLength(10)]
        public string SeatNumber { get; set; } = string.Empty; // 座位号

        public SeatStatus Status { get; set; } = SeatStatus.Available;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    public enum SeatStatus
    {
        Available = 1,    // 可用
        Maintenance = 2,  // 维护中
        Disabled = 3      // 停用
    }

    /// <summary>
    /// 票价实体
    /// </summary>
    public class TicketPrice
    {
        public int Id { get; set; }

        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;

        public int FromStationId { get; set; }
        public virtual Station FromStation { get; set; } = null!;

        public int ToStationId { get; set; }
        public virtual Station ToStation { get; set; } = null!;

        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;

        public decimal Price { get; set; } // 票价

        public int Distance { get; set; } // 里程

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 座位库存实体（按日期、车次、座位类型统计）
    /// </summary>
    public class SeatInventory
    {
        public int Id { get; set; }

        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;

        public DateTime TravelDate { get; set; } // 乘车日期

        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;

        public int TotalSeats { get; set; } // 总座位数

        public int AvailableSeats { get; set; } // 可用座位数

        public int BookedSeats { get; set; } // 已预订座位数

        public int LockedSeats { get; set; } // 锁定座位数（临时锁定，防止超卖）

        public DateTime LastUpdated { get; set; }

        public int Version { get; set; } // 乐观锁版本号

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // 计算属性
        public int RemainingSeats => AvailableSeats - LockedSeats;

        public decimal OccupancyRate => TotalSeats > 0 ? (decimal)BookedSeats / TotalSeats : 0;
    }

    /// <summary>
    /// 订单实体
    /// </summary>
    public class Order
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty; // 订单号

        [Required]
        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // 用户ID（简化处理，实际应该关联用户表）

        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;

        public DateTime TravelDate { get; set; } // 乘车日期

        public int FromStationId { get; set; }
        public virtual Station FromStation { get; set; } = null!;

        public int ToStationId { get; set; }
        public virtual Station ToStation { get; set; } = null!;

        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;

        public int Quantity { get; set; } // 票数

        public decimal UnitPrice { get; set; } // 单价

        public decimal TotalPrice { get; set; } // 总价

        public OrderStatus Status { get; set; } = OrderStatus.Pending;

        public DateTime? PaymentTime { get; set; } // 支付时间

        public DateTime? CancelTime { get; set; } // 取消时间

        public DateTime ExpiryTime { get; set; } // 过期时间

        [StringLength(500)]
        public string Remark { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    public enum OrderStatus
    {
        Pending = 1,     // 待支付
        Paid = 2,        // 已支付
        Cancelled = 3,   // 已取消
        Refunded = 4,    // 已退款
        Expired = 5      // 已过期
    }
}
