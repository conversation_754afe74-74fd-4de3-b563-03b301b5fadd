<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>&#x1f684; Railway Ticket System - 12306&#x706b;&#x8f66;&#x7968;&#x8ba2;&#x7968;&#x7cfb;&#x7edf;&#x6f14;&#x793a;</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="-railway-ticket-system---12306火车票订票系统演示">🚄 Railway Ticket System - 12306火车票订票系统演示</h1>
<p><a href="http://xn--4gqvdonr53a.NET">一个基于.NET</a> Core 6.0的高并发火车票订票系统演示项目，重点展示高并发场景下的票务查询优化和多级缓存策略。</p>
<h2 id="-项目特色">🎯 项目特色</h2>
<h3 id="️-技术架构">🏗️ 技术架构</h3>
<ul>
<li><strong>.NET Core 6.0</strong> Web API</li>
<li><strong>多层架构</strong>：API层、业务逻辑层、数据访问层、缓存层</li>
<li><strong>Entity Framework Core</strong> + SQL Server LocalDB</li>
<li><strong>多级缓存</strong>：IMemoryCache (L1) + Redis (L2)</li>
<li><strong>并发控制</strong>：乐观锁 + 分布式锁防止超卖</li>
<li><strong>结构化日志</strong>：Serilog</li>
<li><strong>API文档</strong>：Swagger UI</li>
</ul>
<h3 id="-核心功能">🚀 核心功能</h3>
<ol>
<li><strong>高并发列车查询</strong>：多级缓存优化，支持热门路线快速响应</li>
<li><strong>防超卖库存管理</strong>：乐观锁版本控制 + 分布式锁</li>
<li><strong>性能监控</strong>：实时缓存命中率和响应时间统计</li>
<li><strong>压力测试</strong>：内置并发测试工具</li>
<li><strong>系统监控</strong>：健康检查和性能指标</li>
</ol>
<h2 id="-快速开始">🚀 快速开始</h2>
<h3 id="1-环境要求">1. 环境要求</h3>
<ul>
<li>.NET 6.0 SDK</li>
<li>SQL Server LocalDB（Visual Studio自带）</li>
<li>可选：Redis（用于L2缓存）</li>
</ul>
<h3 id="2-启动项目">2. 启动项目</h3>
<pre><code class="language-bash"><span class="hljs-comment"># 克隆项目</span>
git <span class="hljs-built_in">clone</span> &lt;repository-url&gt;
<span class="hljs-built_in">cd</span> Railway.TicketSystem

<span class="hljs-comment"># 构建项目</span>
dotnet build

<span class="hljs-comment"># 启动API服务</span>
dotnet run --project Railway.TicketSystem.API
</code></pre>
<h3 id="3-访问应用">3. 访问应用</h3>
<ul>
<li><strong>Swagger UI</strong>: <a href="http://localhost:5088">http://localhost:5088</a></li>
<li><strong>健康检查</strong>: <a href="http://localhost:5088/health">http://localhost:5088/health</a></li>
</ul>
<h2 id="-api接口说明">📊 API接口说明</h2>
<h3 id="-列车查询-apitrain">🚂 列车查询 (<code>/api/train</code>)</h3>
<ul>
<li><code>POST /search</code> - 智能列车查询（带缓存优化）</li>
<li><code>GET /{trainId}</code> - 获取列车详情</li>
<li><code>GET /popular-routes</code> - 获取热门路线</li>
<li><code>POST /warmup</code> - 缓存预热</li>
<li><code>GET /performance</code> - 性能指标监控</li>
</ul>
<h3 id="-库存管理-apiinventory">📦 库存管理 (<code>/api/inventory</code>)</h3>
<ul>
<li><code>GET /</code> - 查询库存信息</li>
<li><code>POST /update</code> - 更新库存（防超卖）</li>
<li><code>POST /lock</code> - 锁定座位</li>
<li><code>POST /unlock</code> - 释放座位</li>
<li><code>POST /book</code> - 预订座位</li>
<li><code>POST /cancel</code> - 取消预订</li>
</ul>
<h3 id="-数据管理-apidata">🔧 数据管理 (<code>/api/data</code>)</h3>
<ul>
<li><code>POST /initialize</code> - 初始化测试数据</li>
<li><code>DELETE /clear</code> - 清空数据</li>
</ul>
<h3 id="-压力测试-apiloadtest">📈 压力测试 (<code>/api/loadtest</code>)</h3>
<ul>
<li><code>POST /concurrent-search</code> - 并发查询压力测试</li>
<li><code>POST /concurrent-inventory</code> - 库存并发更新测试</li>
<li><code>GET /performance-metrics</code> - 系统性能指标</li>
</ul>
<h3 id="-系统监控-apimonitor">📊 系统监控 (<code>/api/monitor</code>)</h3>
<ul>
<li><code>GET /overview</code> - 系统概览</li>
<li><code>GET /performance</code> - 实时性能指标</li>
<li><code>GET /cache-stats</code> - 缓存统计信息</li>
<li><code>GET /health</code> - 健康检查</li>
</ul>
<h2 id="-使用演示">🎮 使用演示</h2>
<h3 id="1-初始化测试数据">1. 初始化测试数据</h3>
<pre><code class="language-bash">POST /api/data/initialize
</code></pre>
<h3 id="2-查询列车信息">2. 查询列车信息</h3>
<pre><code class="language-json">POST /api/train/search
<span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;fromStation&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;北京南&quot;</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;toStation&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;上海虹桥&quot;</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;travelDate&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-15&quot;</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;pageSize&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">20</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="3-运行压力测试">3. 运行压力测试</h3>
<pre><code class="language-bash">POST /api/loadtest/concurrent-search?concurrentUsers=100&amp;requestsPerUser=10
</code></pre>
<h3 id="4-查看系统监控">4. 查看系统监控</h3>
<pre><code class="language-bash">GET /api/monitor/overview
</code></pre>
<h2 id="-配置说明">🔧 配置说明</h2>
<h3 id="数据库配置-appsettingsjson">数据库配置 (appsettings.json)</h3>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;ConnectionStrings&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;DefaultConnection&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;Server=(localdb)\\mssqllocaldb;Database=RailwayTicketSystem;Trusted_Connection=true;&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;Redis&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;localhost:6379&quot;</span>
  <span class="hljs-punctuation">}</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="缓存策略">缓存策略</h3>
<ul>
<li><strong>L1缓存（内存）</strong>：5分钟过期，快速响应</li>
<li><strong>L2缓存（Redis）</strong>：30分钟过期，分布式共享</li>
<li><strong>缓存预热</strong>：支持热门路线预加载</li>
</ul>
<h2 id="-性能特性">📈 性能特性</h2>
<h3 id="高并发优化">高并发优化</h3>
<ul>
<li>多级缓存策略减少数据库压力</li>
<li>异步处理提升响应速度</li>
<li>连接池优化数据库连接</li>
</ul>
<h3 id="防超卖机制">防超卖机制</h3>
<ul>
<li>乐观锁版本控制</li>
<li>分布式锁防止并发冲突</li>
<li>座位锁定机制支持临时预订</li>
</ul>
<h3 id="监控指标">监控指标</h3>
<ul>
<li>缓存命中率统计</li>
<li>响应时间监控</li>
<li>系统资源使用情况</li>
<li>错误率和异常追踪</li>
</ul>
<h2 id="-测试数据">🧪 测试数据</h2>
<p>系统预置了以下测试数据：</p>
<ul>
<li><strong>车站</strong>：北京南、上海虹桥、广州南、深圳北等10个主要车站</li>
<li><strong>列车</strong>：G1、G3、G79等5趟高铁列车</li>
<li><strong>座位类型</strong>：商务座、一等座、二等座、硬卧、软卧</li>
<li><strong>库存</strong>：未来7天的座位库存数据</li>
</ul>
<h2 id="-技术亮点">🔍 技术亮点</h2>
<ol>
<li><strong>多级缓存架构</strong>：L1+L2缓存策略，显著提升查询性能</li>
<li><strong>并发安全设计</strong>：乐观锁+分布式锁双重保护</li>
<li><strong>性能监控体系</strong>：实时指标收集和分析</li>
<li><strong>压力测试工具</strong>：内置并发测试验证系统性能</li>
<li><strong>完整的API文档</strong>：Swagger UI提供交互式文档</li>
</ol>
<h2 id="-开发说明">📝 开发说明</h2>
<h3 id="项目结构">项目结构</h3>
<pre><code>Railway.TicketSystem/
├── Railway.TicketSystem.API/          # Web API层
├── Railway.TicketSystem.Core/         # 业务逻辑层
├── Railway.TicketSystem.Data/         # 数据访问层
├── Railway.TicketSystem.Cache/        # 缓存层
├── Railway.TicketSystem.Models/       # 数据模型层
└── Railway.TicketSystem.Common/       # 公共组件层
</code></pre>
<h3 id="扩展建议">扩展建议</h3>
<ol>
<li>添加用户认证和授权</li>
<li>实现订单支付流程</li>
<li>添加消息队列处理异步任务</li>
<li>实现微服务架构拆分</li>
<li>添加容器化部署支持</li>
</ol>
<h2 id="-许可证">📄 许可证</h2>
<p>本项目仅用于学习和演示目的。</p>
<hr>
<p>🎉 <strong>项目已完成！</strong> 访问 <a href="http://localhost:5088">http://localhost:5088</a> 开始体验高并发火车票订票系统！</p>

            
            
        </body>
        </html>