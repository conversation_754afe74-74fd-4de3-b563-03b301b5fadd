<template>
	<view class="container">
		<!-- 用户信息卡片 -->
		<view class="user-card card">
			<view class="user-info">
				<view class="avatar">
					<image class="avatar-img" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
				</view>
				<view class="user-details">
					<text class="username">{{ userInfo.username || '未登录' }}</text>
					<text class="user-id">ID: {{ userInfo.id || '000000' }}</text>
				</view>
				<view class="login-btn" @click="handleLogin" v-if="!isLoggedIn">
					<text class="login-text">登录</text>
				</view>
			</view>
		</view>
		
		<!-- 快捷功能 -->
		<view class="quick-menu card">
			<view class="menu-title">快捷功能</view>
			<view class="menu-grid">
				<view class="menu-item" @click="goToOrderList">
					<text class="menu-icon">📋</text>
					<text class="menu-text">我的订单</text>
				</view>
				<view class="menu-item" @click="goToTickets">
					<text class="menu-icon">🎫</text>
					<text class="menu-text">我的车票</text>
				</view>
				<view class="menu-item" @click="goToPassengers">
					<text class="menu-icon">👥</text>
					<text class="menu-text">常用联系人</text>
				</view>
				<view class="menu-item" @click="goToRefund">
					<text class="menu-icon">💰</text>
					<text class="menu-text">退票改签</text>
				</view>
			</view>
		</view>
		
		<!-- 功能列表 -->
		<view class="function-list">
			<view class="function-item card" @click="goToSystemMonitor">
				<view class="function-content">
					<text class="function-icon">📊</text>
					<text class="function-text">系统监控</text>
				</view>
				<text class="function-arrow">></text>
			</view>
			
			<view class="function-item card" @click="goToLoadTest">
				<view class="function-content">
					<text class="function-icon">⚡</text>
					<text class="function-text">压力测试</text>
				</view>
				<text class="function-arrow">></text>
			</view>
			
			<view class="function-item card" @click="goToSettings">
				<view class="function-content">
					<text class="function-icon">⚙️</text>
					<text class="function-text">设置</text>
				</view>
				<text class="function-arrow">></text>
			</view>
			
			<view class="function-item card" @click="goToHelp">
				<view class="function-content">
					<text class="function-icon">❓</text>
					<text class="function-text">帮助与反馈</text>
				</view>
				<text class="function-arrow">></text>
			</view>
			
			<view class="function-item card" @click="goToAbout">
				<view class="function-content">
					<text class="function-icon">ℹ️</text>
					<text class="function-text">关于我们</text>
				</view>
				<text class="function-arrow">></text>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section" v-if="isLoggedIn">
			<button class="logout-btn" @click="handleLogout">退出登录</button>
		</view>
		
		<!-- 版本信息 -->
		<view class="version-info">
			<text class="version-text">版本号：v1.0.0</text>
			<text class="copyright-text">© 2024 12306火车票订票系统演示</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					id: '123456',
					username: '演示用户',
					avatar: '',
					phone: '138****8888',
					email: '<EMAIL>'
				},
				isLoggedIn: true // 演示用，默认已登录
			}
		},
		onLoad() {
			this.loadUserInfo()
		},
		onShow() {
			// 页面显示时刷新用户信息
			this.loadUserInfo()
		},
		methods: {
			loadUserInfo() {
				// 从本地存储加载用户信息
				try {
					const userInfo = uni.getStorageSync('userInfo')
					if (userInfo) {
						this.userInfo = userInfo
						this.isLoggedIn = true
					}
				} catch (error) {
					console.error('加载用户信息失败:', error)
				}
			},
			
			handleLogin() {
				// 模拟登录
				uni.showModal({
					title: '登录',
					content: '这是演示版本，点击确定模拟登录',
					success: (res) => {
						if (res.confirm) {
							this.isLoggedIn = true
							this.userInfo = {
								id: '123456',
								username: '演示用户',
								avatar: '',
								phone: '138****8888',
								email: '<EMAIL>'
							}
							
							// 保存到本地存储
							uni.setStorageSync('userInfo', this.userInfo)
							this.$toast('登录成功')
						}
					}
				})
			},
			
			async handleLogout() {
				try {
					const confirmed = await this.$confirm('确定要退出登录吗？')
					if (confirmed) {
						this.isLoggedIn = false
						this.userInfo = {}
						
						// 清除本地存储
						uni.removeStorageSync('userInfo')
						this.$toast('已退出登录')
					}
				} catch (error) {
					// 用户取消
				}
			},
			
			goToOrderList() {
				uni.switchTab({
					url: '/pages/order-list/order-list'
				})
			},
			
			goToTickets() {
				this.$toast('功能开发中...')
			},
			
			goToPassengers() {
				this.$toast('功能开发中...')
			},
			
			goToRefund() {
				this.$toast('功能开发中...')
			},
			
			goToSystemMonitor() {
				uni.navigateTo({
					url: '/pages/system-monitor/system-monitor'
				})
			},
			
			goToLoadTest() {
				uni.navigateTo({
					url: '/pages/load-test/load-test'
				})
			},
			
			goToSettings() {
				this.$toast('功能开发中...')
			},
			
			goToHelp() {
				this.$toast('功能开发中...')
			},
			
			goToAbout() {
				uni.showModal({
					title: '关于我们',
					content: '12306火车票订票系统演示\n\n这是一个基于.NET Core 6.0和UniApp开发的高并发火车票订票系统演示项目。\n\n主要特性：\n• 多级缓存优化\n• 防超卖机制\n• 高并发处理\n• 性能监控\n• 压力测试',
					showCancel: false,
					confirmText: '知道了'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
		padding: 20rpx;
	}
	
	.user-card {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}
	
	.user-info {
		display: flex;
		align-items: center;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 30rpx;
		background-color: #F0F8FF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.avatar-img {
		width: 100%;
		height: 100%;
	}
	
	.user-details {
		flex: 1;
	}
	
	.username {
		display: block;
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
	}
	
	.user-id {
		font-size: 24rpx;
		color: #999999;
	}
	
	.login-btn {
		padding: 16rpx 32rpx;
		background-color: #0066CC;
		border-radius: 20rpx;
	}
	
	.login-text {
		font-size: 28rpx;
		color: #FFFFFF;
	}
	
	.quick-menu {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}
	
	.menu-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.menu-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30rpx;
	}
	
	.menu-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
	}
	
	.menu-icon {
		font-size: 48rpx;
		margin-bottom: 12rpx;
	}
	
	.menu-text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.function-list {
		margin-bottom: 40rpx;
	}
	
	.function-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		margin-bottom: 20rpx;
	}
	
	.function-content {
		display: flex;
		align-items: center;
	}
	
	.function-icon {
		font-size: 40rpx;
		margin-right: 24rpx;
	}
	
	.function-text {
		font-size: 32rpx;
		color: #333333;
	}
	
	.function-arrow {
		font-size: 24rpx;
		color: #999999;
	}
	
	.logout-section {
		margin-bottom: 40rpx;
	}
	
	.logout-btn {
		width: 100%;
		height: 88rpx;
		background-color: #FFFFFF;
		color: #FF3333;
		border: 2rpx solid #FF3333;
		border-radius: 12rpx;
		font-size: 32rpx;
	}
	
	.version-info {
		text-align: center;
		padding: 40rpx 20rpx;
	}
	
	.version-text {
		display: block;
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
	}
	
	.copyright-text {
		font-size: 20rpx;
		color: #CCCCCC;
	}
</style>
