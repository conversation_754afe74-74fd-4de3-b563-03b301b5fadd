﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.API\\Railway.TicketSystem.API.csproj","projectName":"Railway.TicketSystem.API","projectPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.API\\Railway.TicketSystem.API.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.API\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Cache\\Railway.TicketSystem.Cache.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Cache\\Railway.TicketSystem.Cache.csproj"},"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Core\\Railway.TicketSystem.Core.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Core\\Railway.TicketSystem.Core.csproj"},"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Data\\Railway.TicketSystem.Data.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Data\\Railway.TicketSystem.Data.csproj"},"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Models\\Railway.TicketSystem.Models.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Railway.TicketSystem\\Railway.TicketSystem.Models\\Railway.TicketSystem.Models.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.100"}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[6.0.0, )"},"Microsoft.EntityFrameworkCore.Tools":{"target":"Package","version":"[6.0.0, )"},"Microsoft.Extensions.Caching.StackExchangeRedis":{"target":"Package","version":"[6.0.0, )"},"Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore":{"target":"Package","version":"[6.0.0, )"},"Serilog.AspNetCore":{"target":"Package","version":"[6.0.0, )"},"Serilog.Sinks.Console":{"target":"Package","version":"[4.0.1, )"},"Serilog.Sinks.File":{"target":"Package","version":"[5.0.0, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[6.5.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}