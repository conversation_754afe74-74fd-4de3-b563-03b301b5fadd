using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 列车座位实体
    /// </summary>
    public class TrainSeat
    {
        public int Id { get; set; }
        
        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;
        
        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;
        
        public int CarriageNumber { get; set; } // 车厢号
        
        [Required]
        [StringLength(10)]
        public string SeatNumber { get; set; } = string.Empty; // 座位号
        
        public SeatStatus Status { get; set; } = SeatStatus.Available;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
    }
    
    public enum SeatStatus
    {
        Available = 1,    // 可用
        Maintenance = 2,  // 维护中
        Disabled = 3      // 停用
    }
}
