import { createSSRApp } from 'vue'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  
  // 全局配置
  app.config.globalProperties.$baseUrl = 'http://localhost:5088/api'
  
  // 全局方法
  app.config.globalProperties.$toast = (title, icon = 'none', duration = 2000) => {
    uni.showToast({
      title,
      icon,
      duration
    })
  }
  
  app.config.globalProperties.$loading = (title = '加载中...') => {
    uni.showLoading({
      title,
      mask: true
    })
  }
  
  app.config.globalProperties.$hideLoading = () => {
    uni.hideLoading()
  }
  
  app.config.globalProperties.$confirm = (content, title = '提示') => {
    return new Promise((resolve, reject) => {
      uni.showModal({
        title,
        content,
        success: (res) => {
          if (res.confirm) {
            resolve(true)
          } else {
            reject(false)
          }
        },
        fail: () => {
          reject(false)
        }
      })
    })
  }
  
  // 格式化日期
  app.config.globalProperties.$formatDate = (date, format = 'YYYY-MM-DD') => {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  }
  
  // 格式化时间
  app.config.globalProperties.$formatTime = (timeSpan) => {
    if (!timeSpan) return ''
    const parts = timeSpan.split(':')
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`
    }
    return timeSpan
  }
  
  // 计算时长
  app.config.globalProperties.$formatDuration = (duration) => {
    if (!duration) return ''
    const parts = duration.split(':')
    if (parts.length >= 2) {
      const hours = parseInt(parts[0])
      const minutes = parseInt(parts[1])
      return `${hours}小时${minutes}分钟`
    }
    return duration
  }
  
  return {
    app
  }
}
