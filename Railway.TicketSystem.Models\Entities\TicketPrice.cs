using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 票价实体
    /// </summary>
    public class TicketPrice
    {
        public int Id { get; set; }
        
        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;
        
        public int FromStationId { get; set; }
        public virtual Station FromStation { get; set; } = null!;
        
        public int ToStationId { get; set; }
        public virtual Station ToStation { get; set; } = null!;
        
        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;
        
        public decimal Price { get; set; } // 票价
        
        public int Distance { get; set; } // 里程
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
    }
}
