using Microsoft.EntityFrameworkCore;
using Railway.TicketSystem.Models.Entities;

namespace Railway.TicketSystem.Data.Repositories
{
    public class TrainRepository : ITrainRepository
    {
        private readonly RailwayDbContext _context;

        public TrainRepository(RailwayDbContext context)
        {
            _context = context;
        }

        public async Task<List<Train>> SearchTrainsAsync(string fromStation, string toStation, DateTime travelDate)
        {
            // 查找经过起始站和目的站的列车
            var query = from train in _context.Trains
                        join fromRoute in _context.TrainRoutes on train.Id equals fromRoute.TrainId
                        join fromSt in _context.Stations on fromRoute.StationId equals fromSt.Id
                        join toRoute in _context.TrainRoutes on train.Id equals toRoute.TrainId
                        join toSt in _context.Stations on toRoute.StationId equals toSt.Id
                        where train.Status == TrainStatus.Active
                              && fromSt.StationName == fromStation
                              && toSt.StationName == toStation
                              && fromRoute.StopOrder < toRoute.StopOrder
                        select train;

            return await query
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Include(t => t.TrainRoutes)
                    .ThenInclude(tr => tr.Station)
                .Distinct()
                .OrderBy(t => t.DepartureTime)
                .ToListAsync();
        }

        public async Task<Train?> GetTrainByIdAsync(int trainId)
        {
            return await _context.Trains
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Include(t => t.TrainRoutes)
                    .ThenInclude(tr => tr.Station)
                .FirstOrDefaultAsync(t => t.Id == trainId);
        }

        public async Task<Train?> GetTrainByNumberAsync(string trainNumber)
        {
            return await _context.Trains
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Include(t => t.TrainRoutes)
                    .ThenInclude(tr => tr.Station)
                .FirstOrDefaultAsync(t => t.TrainNumber == trainNumber);
        }

        public async Task<List<Train>> GetTrainsByRouteAsync(int fromStationId, int toStationId)
        {
            var query = from train in _context.Trains
                        join fromRoute in _context.TrainRoutes on train.Id equals fromRoute.TrainId
                        join toRoute in _context.TrainRoutes on train.Id equals toRoute.TrainId
                        where train.Status == TrainStatus.Active
                              && fromRoute.StationId == fromStationId
                              && toRoute.StationId == toStationId
                              && fromRoute.StopOrder < toRoute.StopOrder
                        select train;

            return await query
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Distinct()
                .OrderBy(t => t.DepartureTime)
                .ToListAsync();
        }

        public async Task<List<TrainRoute>> GetTrainRoutesAsync(int trainId)
        {
            return await _context.TrainRoutes
                .Include(tr => tr.Station)
                .Where(tr => tr.TrainId == trainId && tr.IsActive)
                .OrderBy(tr => tr.StopOrder)
                .ToListAsync();
        }

        public async Task<List<TicketPrice>> GetTicketPricesAsync(int trainId, int fromStationId, int toStationId)
        {
            return await _context.TicketPrices
                .Include(tp => tp.SeatType)
                .Where(tp => tp.TrainId == trainId 
                            && tp.FromStationId == fromStationId 
                            && tp.ToStationId == toStationId
                            && tp.IsActive)
                .OrderBy(tp => tp.SeatType.SortOrder)
                .ToListAsync();
        }

        public async Task<bool> IsValidRouteAsync(int trainId, int fromStationId, int toStationId)
        {
            var fromRoute = await _context.TrainRoutes
                .FirstOrDefaultAsync(tr => tr.TrainId == trainId && tr.StationId == fromStationId);
            
            var toRoute = await _context.TrainRoutes
                .FirstOrDefaultAsync(tr => tr.TrainId == trainId && tr.StationId == toStationId);

            return fromRoute != null && toRoute != null && fromRoute.StopOrder < toRoute.StopOrder;
        }

        public async Task<List<Train>> GetActiveTrainsAsync()
        {
            return await _context.Trains
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Where(t => t.Status == TrainStatus.Active)
                .OrderBy(t => t.TrainNumber)
                .ToListAsync();
        }

        public async Task<List<Train>> GetTrainsByTypeAsync(string trainType)
        {
            return await _context.Trains
                .Include(t => t.StartStation)
                .Include(t => t.EndStation)
                .Where(t => t.TrainType == trainType && t.Status == TrainStatus.Active)
                .OrderBy(t => t.TrainNumber)
                .ToListAsync();
        }

        public async Task<List<Station>> GetAllStationsAsync()
        {
            return await _context.Stations
                .Where(s => s.IsActive)
                .OrderBy(s => s.StationName)
                .ToListAsync();
        }

        public async Task<Station?> GetStationByNameAsync(string stationName)
        {
            return await _context.Stations
                .FirstOrDefaultAsync(s => s.StationName == stationName && s.IsActive);
        }
    }
}
