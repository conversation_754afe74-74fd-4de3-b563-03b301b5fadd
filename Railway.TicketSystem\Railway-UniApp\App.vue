<template>
	<view id="app">
		<!-- 应用入口 -->
	</view>
</template>

<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 应用启动时的初始化
			this.initApp()
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			initApp() {
				// 检查网络状态
				uni.getNetworkType({
					success: (res) => {
						console.log('网络类型：', res.networkType)
						if (res.networkType === 'none') {
							uni.showToast({
								title: '网络连接异常',
								icon: 'none'
							})
						}
					}
				})
				
				// 获取系统信息
				uni.getSystemInfo({
					success: (res) => {
						console.log('系统信息：', res)
						// 存储系统信息到全局
						getApp().globalData.systemInfo = res
					}
				})
			}
		},
		globalData: {
			systemInfo: null,
			userInfo: null,
			baseUrl: 'http://localhost:5088/api'
		}
	}
</script>

<style lang="scss">
	/* 全局样式 */
	@import './uni.scss';
	
	/* 重置样式 */
	* {
		box-sizing: border-box;
	}
	
	page {
		background-color: #F5F5F5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
	}
	
	/* 通用类 */
	.container {
		padding: 20rpx;
	}
	
	.card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 20rpx;
		overflow: hidden;
	}
	
	.btn-primary {
		background-color: #0066CC;
		color: #FFFFFF;
		border: none;
		border-radius: 8rpx;
		padding: 24rpx 48rpx;
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.btn-primary:active {
		background-color: #0052A3;
	}
	
	.btn-secondary {
		background-color: #FFFFFF;
		color: #0066CC;
		border: 2rpx solid #0066CC;
		border-radius: 8rpx;
		padding: 22rpx 46rpx;
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.btn-secondary:active {
		background-color: #F0F8FF;
	}
	
	.text-primary {
		color: #0066CC;
	}
	
	.text-secondary {
		color: #666666;
	}
	
	.text-muted {
		color: #999999;
	}
	
	.text-danger {
		color: #FF3333;
	}
	
	.text-success {
		color: #00CC66;
	}
	
	.text-warning {
		color: #FF6600;
	}
	
	.flex {
		display: flex;
	}
	
	.flex-column {
		flex-direction: column;
	}
	
	.flex-center {
		justify-content: center;
		align-items: center;
	}
	
	.flex-between {
		justify-content: space-between;
		align-items: center;
	}
	
	.flex-1 {
		flex: 1;
	}
	
	.mt-10 {
		margin-top: 20rpx;
	}
	
	.mt-20 {
		margin-top: 40rpx;
	}
	
	.mb-10 {
		margin-bottom: 20rpx;
	}
	
	.mb-20 {
		margin-bottom: 40rpx;
	}
	
	.ml-10 {
		margin-left: 20rpx;
	}
	
	.mr-10 {
		margin-right: 20rpx;
	}
	
	.p-10 {
		padding: 20rpx;
	}
	
	.p-20 {
		padding: 40rpx;
	}
	
	.px-10 {
		padding-left: 20rpx;
		padding-right: 20rpx;
	}
	
	.py-10 {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}
	
	.font-12 {
		font-size: 24rpx;
	}
	
	.font-14 {
		font-size: 28rpx;
	}
	
	.font-16 {
		font-size: 32rpx;
	}
	
	.font-18 {
		font-size: 36rpx;
	}
	
	.font-20 {
		font-size: 40rpx;
	}
	
	.font-bold {
		font-weight: 600;
	}
	
	.text-center {
		text-align: center;
	}
	
	.text-left {
		text-align: left;
	}
	
	.text-right {
		text-align: right;
	}
	
	.border-bottom {
		border-bottom: 1rpx solid #E5E5E5;
	}
	
	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
		color: #999999;
	}
	
	.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
		color: #999999;
	}
	
	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
	}
</style>
