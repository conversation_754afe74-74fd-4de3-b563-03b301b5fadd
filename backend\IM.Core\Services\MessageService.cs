using Microsoft.EntityFrameworkCore;
using IM.Core.DTOs;
using IM.Data;
using IM.Models;

namespace IM.Core.Services
{
    public class MessageService : IMessageService
    {
        private readonly IMDbContext _context;

        public MessageService(IMDbContext context)
        {
            _context = context;
        }

        public async Task<MessageDto?> SendPrivateMessageAsync(int senderId, SendMessageDto messageDto)
        {
            if (!messageDto.ReceiverId.HasValue) return null;

            // 检查是否为好友关系
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    ((f.RequesterId == senderId && f.AddresseeId == messageDto.ReceiverId) ||
                     (f.RequesterId == messageDto.ReceiverId && f.AddresseeId == senderId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null) return null;

            var message = new Message
            {
                SenderId = senderId,
                ReceiverId = messageDto.ReceiverId.Value,
                Content = messageDto.Content,
                Type = messageDto.Type,
                SentAt = DateTime.UtcNow,
                FileName = messageDto.FileName,
                FileUrl = messageDto.FileUrl,
                FileSize = messageDto.FileSize,
                MimeType = messageDto.MimeType
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            return await GetMessageDtoAsync(message.Id);
        }

        public async Task<MessageDto?> SendGroupMessageAsync(int senderId, SendMessageDto messageDto)
        {
            if (!messageDto.ChatRoomId.HasValue) return null;

            // 检查用户是否为群成员
            var membership = await _context.ChatRoomMembers
                .FirstOrDefaultAsync(m => m.ChatRoomId == messageDto.ChatRoomId && m.UserId == senderId && m.IsActive);

            if (membership == null) return null;

            var message = new Message
            {
                SenderId = senderId,
                ChatRoomId = messageDto.ChatRoomId.Value,
                Content = messageDto.Content,
                Type = messageDto.Type,
                SentAt = DateTime.UtcNow,
                FileName = messageDto.FileName,
                FileUrl = messageDto.FileUrl,
                FileSize = messageDto.FileSize,
                MimeType = messageDto.MimeType
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            return await GetMessageDtoAsync(message.Id);
        }

        public async Task<MessageHistoryDto> GetPrivateMessageHistoryAsync(int userId, int otherUserId, int pageNumber = 1, int pageSize = 50)
        {
            var query = _context.Messages
                .Where(m => !m.IsDeleted &&
                           ((m.SenderId == userId && m.ReceiverId == otherUserId) ||
                            (m.SenderId == otherUserId && m.ReceiverId == userId)))
                .OrderByDescending(m => m.SentAt);

            var totalCount = await query.CountAsync();
            var messages = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(m => m.Sender)
                .Include(m => m.Receiver)
                .ToListAsync();

            return new MessageHistoryDto
            {
                Messages = messages.Select(MapToMessageDto).ToList(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                HasNextPage = pageNumber * pageSize < totalCount,
                HasPreviousPage = pageNumber > 1
            };
        }

        public async Task<MessageHistoryDto> GetGroupMessageHistoryAsync(int userId, int chatRoomId, int pageNumber = 1, int pageSize = 50)
        {
            // 检查用户是否为群成员
            var membership = await _context.ChatRoomMembers
                .FirstOrDefaultAsync(m => m.ChatRoomId == chatRoomId && m.UserId == userId && m.IsActive);

            if (membership == null)
            {
                return new MessageHistoryDto();
            }

            var query = _context.Messages
                .Where(m => !m.IsDeleted && m.ChatRoomId == chatRoomId)
                .OrderByDescending(m => m.SentAt);

            var totalCount = await query.CountAsync();
            var messages = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(m => m.Sender)
                .Include(m => m.ChatRoom)
                .ToListAsync();

            return new MessageHistoryDto
            {
                Messages = messages.Select(MapToMessageDto).ToList(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                HasNextPage = pageNumber * pageSize < totalCount,
                HasPreviousPage = pageNumber > 1
            };
        }

        public async Task<bool> MarkMessageAsReadAsync(int userId, int messageId)
        {
            var message = await _context.Messages.FindAsync(messageId);
            if (message == null || message.ReceiverId != userId) return false;

            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<List<MessageDto>> GetUnreadMessagesAsync(int userId)
        {
            var messages = await _context.Messages
                .Where(m => m.ReceiverId == userId && !m.IsRead && !m.IsDeleted)
                .Include(m => m.Sender)
                .Include(m => m.ChatRoom)
                .OrderBy(m => m.SentAt)
                .ToListAsync();

            return messages.Select(MapToMessageDto).ToList();
        }

        public async Task<bool> DeleteMessageAsync(int userId, int messageId)
        {
            var message = await _context.Messages.FindAsync(messageId);
            if (message == null || message.SenderId != userId) return false;

            message.IsDeleted = true;
            message.DeletedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return true;
        }

        private async Task<MessageDto?> GetMessageDtoAsync(int messageId)
        {
            var message = await _context.Messages
                .Include(m => m.Sender)
                .Include(m => m.Receiver)
                .Include(m => m.ChatRoom)
                .FirstOrDefaultAsync(m => m.Id == messageId);

            return message != null ? MapToMessageDto(message) : null;
        }

        private static MessageDto MapToMessageDto(Message message)
        {
            return new MessageDto
            {
                Id = message.Id,
                Content = message.Content,
                Type = message.Type,
                SenderId = message.SenderId,
                Sender = new UserDto
                {
                    Id = message.Sender.Id,
                    Username = message.Sender.Username,
                    DisplayName = message.Sender.DisplayName,
                    Avatar = message.Sender.Avatar
                },
                ReceiverId = message.ReceiverId,
                Receiver = message.Receiver != null ? new UserDto
                {
                    Id = message.Receiver.Id,
                    Username = message.Receiver.Username,
                    DisplayName = message.Receiver.DisplayName,
                    Avatar = message.Receiver.Avatar
                } : null,
                ChatRoomId = message.ChatRoomId,
                IsRead = message.IsRead,
                SentAt = message.SentAt,
                ReadAt = message.ReadAt,
                FileName = message.FileName,
                FileUrl = message.FileUrl,
                FileSize = message.FileSize,
                MimeType = message.MimeType
            };
        }
    }
}
