using IM.Core.DTOs;

namespace IM.Core.Services
{
    public interface IMessageService
    {
        Task<MessageDto?> SendPrivateMessageAsync(int senderId, SendMessageDto messageDto);
        Task<MessageDto?> SendGroupMessageAsync(int senderId, SendMessageDto messageDto);
        Task<MessageHistoryDto> GetPrivateMessageHistoryAsync(int userId, int otherUserId, int pageNumber = 1, int pageSize = 50);
        Task<MessageHistoryDto> GetGroupMessageHistoryAsync(int userId, int chatRoomId, int pageNumber = 1, int pageSize = 50);
        Task<bool> MarkMessageAsReadAsync(int userId, int messageId);
        Task<List<MessageDto>> GetUnreadMessagesAsync(int userId);
        Task<bool> DeleteMessageAsync(int userId, int messageId);
    }
}
