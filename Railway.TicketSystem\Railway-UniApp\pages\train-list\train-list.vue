<template>
	<view class="container">
		<!-- 搜索条件栏 -->
		<view class="search-bar card">
			<view class="search-info">
				<view class="route-info">
					<text class="route-text">{{ searchParams.fromStation }} → {{ searchParams.toStation }}</text>
					<text class="date-text">{{ formatDate(searchParams.travelDate) }}</text>
				</view>
				<view class="modify-btn" @click="goBack">
					<text class="modify-text">修改</text>
				</view>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar card">
			<view class="filter-item" @click="toggleFilter('time')">
				<text class="filter-text">出发时间</text>
				<text class="filter-arrow">▼</text>
			</view>
			<view class="filter-item" @click="toggleFilter('type')">
				<text class="filter-text">车次类型</text>
				<text class="filter-arrow">▼</text>
			</view>
			<view class="filter-item" @click="toggleSort">
				<text class="filter-text">{{ sortType === 'time' ? '时间排序' : '价格排序' }}</text>
				<text class="filter-arrow">⇅</text>
			</view>
		</view>
		
		<!-- 列车列表 -->
		<view class="train-list">
			<!-- 加载中 -->
			<view v-if="loading" class="loading">
				<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
			</view>
			
			<!-- 列车卡片 -->
			<view 
				v-for="train in filteredTrains" 
				:key="train.trainId"
				class="train-card card"
				@click="selectTrain(train)"
			>
				<view class="train-header">
					<view class="train-number">{{ train.trainNumber }}</view>
					<view class="train-type">{{ getTrainTypeText(train.trainType) }}</view>
				</view>
				
				<view class="train-route">
					<view class="departure">
						<text class="time">{{ formatTime(train.departureTime) }}</text>
						<text class="station">{{ train.fromStation }}</text>
					</view>
					
					<view class="duration">
						<text class="duration-text">{{ formatDuration(train.duration) }}</text>
						<view class="duration-line"></view>
					</view>
					
					<view class="arrival">
						<text class="time">{{ formatTime(train.arrivalTime) }}</text>
						<text class="station">{{ train.toStation }}</text>
					</view>
				</view>
				
				<view class="seat-info">
					<view 
						v-for="seat in train.seatAvailabilities" 
						:key="seat.seatTypeId"
						class="seat-item"
						:class="{ 'no-ticket': seat.availableSeats === 0 }"
					>
						<text class="seat-type">{{ seat.seatTypeName }}</text>
						<text class="seat-price">¥{{ seat.price }}</text>
						<text class="seat-status">{{ getSeatStatusText(seat) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && filteredTrains.length === 0" class="empty">
				<text class="empty-icon">🚄</text>
				<text class="empty-text">暂无符合条件的车次</text>
				<button class="empty-btn btn-secondary" @click="goBack">重新搜索</button>
			</view>
		</view>
		
		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">{{ filterTitle }}</text>
					<text class="popup-close" @click="closeFilter">×</text>
				</view>
				
				<!-- 时间筛选 -->
				<view v-if="currentFilter === 'time'" class="filter-content">
					<view 
						v-for="timeRange in timeRanges" 
						:key="timeRange.key"
						class="filter-option"
						:class="{ active: selectedTimeRange === timeRange.key }"
						@click="selectTimeRange(timeRange.key)"
					>
						<text class="option-text">{{ timeRange.label }}</text>
						<text v-if="selectedTimeRange === timeRange.key" class="option-check">✓</text>
					</view>
				</view>
				
				<!-- 车次类型筛选 -->
				<view v-if="currentFilter === 'type'" class="filter-content">
					<view 
						v-for="trainType in trainTypes" 
						:key="trainType.key"
						class="filter-option"
						:class="{ active: selectedTrainTypes.includes(trainType.key) }"
						@click="toggleTrainType(trainType.key)"
					>
						<text class="option-text">{{ trainType.label }}</text>
						<text v-if="selectedTrainTypes.includes(trainType.key)" class="option-check">✓</text>
					</view>
				</view>
				
				<view class="filter-actions">
					<button class="filter-reset btn-secondary" @click="resetFilter">重置</button>
					<button class="filter-confirm btn-primary" @click="confirmFilter">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { searchTrains } from '@/api/train.js'
	
	export default {
		data() {
			return {
				searchParams: {},
				trains: [],
				filteredTrains: [],
				loading: false,
				loadingText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				
				// 筛选相关
				currentFilter: '',
				filterTitle: '',
				selectedTimeRange: 'all',
				selectedTrainTypes: [],
				sortType: 'time', // 'time' | 'price'
				
				// 筛选选项
				timeRanges: [
					{ key: 'all', label: '全部时间' },
					{ key: 'morning', label: '06:00-12:00' },
					{ key: 'afternoon', label: '12:00-18:00' },
					{ key: 'evening', label: '18:00-24:00' }
				],
				trainTypes: [
					{ key: 'G', label: '高速动车' },
					{ key: 'D', label: '动车' },
					{ key: 'T', label: '特快' },
					{ key: 'K', label: '快速' },
					{ key: 'Z', label: '直达' }
				]
			}
		},
		onLoad(options) {
			if (options.params) {
				this.searchParams = JSON.parse(decodeURIComponent(options.params))
				this.loadTrains()
			}
		},
		methods: {
			async loadTrains() {
				this.loading = true
				try {
					const response = await searchTrains(this.searchParams)
					this.trains = response.trains || []
					this.applyFilters()
				} catch (error) {
					console.error('加载列车失败:', error)
					this.$toast('加载失败，请重试')
				} finally {
					this.loading = false
				}
			},
			
			applyFilters() {
				let filtered = [...this.trains]
				
				// 时间筛选
				if (this.selectedTimeRange !== 'all') {
					filtered = filtered.filter(train => {
						const hour = parseInt(train.departureTime.split(':')[0])
						switch (this.selectedTimeRange) {
							case 'morning': return hour >= 6 && hour < 12
							case 'afternoon': return hour >= 12 && hour < 18
							case 'evening': return hour >= 18 || hour < 6
							default: return true
						}
					})
				}
				
				// 车次类型筛选
				if (this.selectedTrainTypes.length > 0) {
					filtered = filtered.filter(train => 
						this.selectedTrainTypes.includes(train.trainType)
					)
				}
				
				// 排序
				if (this.sortType === 'time') {
					filtered.sort((a, b) => a.departureTime.localeCompare(b.departureTime))
				} else {
					filtered.sort((a, b) => {
						const priceA = Math.min(...a.seatAvailabilities.map(s => s.price))
						const priceB = Math.min(...b.seatAvailabilities.map(s => s.price))
						return priceA - priceB
					})
				}
				
				this.filteredTrains = filtered
			},
			
			toggleFilter(type) {
				this.currentFilter = type
				this.filterTitle = type === 'time' ? '出发时间' : '车次类型'
				this.$refs.filterPopup.open()
			},
			
			closeFilter() {
				this.$refs.filterPopup.close()
			},
			
			selectTimeRange(range) {
				this.selectedTimeRange = range
			},
			
			toggleTrainType(type) {
				const index = this.selectedTrainTypes.indexOf(type)
				if (index > -1) {
					this.selectedTrainTypes.splice(index, 1)
				} else {
					this.selectedTrainTypes.push(type)
				}
			},
			
			resetFilter() {
				if (this.currentFilter === 'time') {
					this.selectedTimeRange = 'all'
				} else {
					this.selectedTrainTypes = []
				}
			},
			
			confirmFilter() {
				this.applyFilters()
				this.closeFilter()
			},
			
			toggleSort() {
				this.sortType = this.sortType === 'time' ? 'price' : 'time'
				this.applyFilters()
			},
			
			selectTrain(train) {
				uni.navigateTo({
					url: `/pages/train-detail/train-detail?trainId=${train.trainId}&travelDate=${this.searchParams.travelDate}&fromStation=${this.searchParams.fromStation}&toStation=${this.searchParams.toStation}`
				})
			},
			
			goBack() {
				uni.navigateBack()
			},
			
			formatDate(dateStr) {
				const date = new Date(dateStr)
				const month = date.getMonth() + 1
				const day = date.getDate()
				const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
				const weekDay = weekDays[date.getDay()]
				return `${month}月${day}日 ${weekDay}`
			},
			
			formatTime(timeStr) {
				return timeStr.substring(0, 5)
			},
			
			formatDuration(duration) {
				const parts = duration.split(':')
				const hours = parseInt(parts[0])
				const minutes = parseInt(parts[1])
				return `${hours}小时${minutes}分`
			},
			
			getTrainTypeText(type) {
				const typeMap = {
					'G': '高速动车',
					'D': '动车',
					'T': '特快',
					'K': '快速',
					'Z': '直达'
				}
				return typeMap[type] || type
			},
			
			getSeatStatusText(seat) {
				if (seat.availableSeats === 0) return '无票'
				if (seat.availableSeats < 10) return '紧张'
				return '有票'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
	}

	.search-bar {
		margin: 20rpx;
		padding: 30rpx;
	}

	.search-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.route-info {
		flex: 1;
	}

	.route-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-right: 20rpx;
	}

	.date-text {
		font-size: 24rpx;
		color: #666666;
	}

	.modify-btn {
		padding: 12rpx 24rpx;
		background-color: #F0F8FF;
		border: 2rpx solid #0066CC;
		border-radius: 20rpx;
	}

	.modify-text {
		font-size: 24rpx;
		color: #0066CC;
	}

	.filter-bar {
		margin: 0 20rpx 20rpx;
		padding: 20rpx 30rpx;
		display: flex;
		justify-content: space-between;
	}

	.filter-item {
		display: flex;
		align-items: center;
		padding: 12rpx 20rpx;
		background-color: #F8F9FA;
		border-radius: 20rpx;
	}

	.filter-text {
		font-size: 24rpx;
		color: #666666;
		margin-right: 8rpx;
	}

	.filter-arrow {
		font-size: 20rpx;
		color: #999999;
	}

	.train-list {
		padding: 0 20rpx;
	}

	.train-card {
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.train-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.train-number {
		font-size: 36rpx;
		font-weight: 600;
		color: #0066CC;
	}

	.train-type {
		font-size: 24rpx;
		color: #666666;
		padding: 8rpx 16rpx;
		background-color: #F0F8FF;
		border-radius: 12rpx;
	}

	.train-route {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.departure,
	.arrival {
		flex: 1;
		text-align: center;
	}

	.time {
		display: block;
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.station {
		font-size: 24rpx;
		color: #666666;
	}

	.duration {
		flex: 1;
		text-align: center;
		position: relative;
	}

	.duration-text {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 12rpx;
		display: block;
	}

	.duration-line {
		height: 2rpx;
		background-color: #E5E5E5;
		position: relative;

		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: -4rpx;
			width: 10rpx;
			height: 10rpx;
			background-color: #0066CC;
			border-radius: 50%;
		}

		&::after {
			content: '';
			position: absolute;
			right: 0;
			top: -4rpx;
			width: 10rpx;
			height: 10rpx;
			background-color: #FF6600;
			border-radius: 50%;
		}
	}

	.seat-info {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.seat-item {
		flex: 1;
		min-width: 200rpx;
		padding: 20rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
		text-align: center;

		&.no-ticket {
			background-color: #FFF5F5;
		}
	}

	.seat-type {
		display: block;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 8rpx;
	}

	.seat-price {
		display: block;
		font-size: 32rpx;
		font-weight: 600;
		color: #FF6600;
		margin-bottom: 8rpx;
	}

	.seat-status {
		font-size: 24rpx;

		&:contains('无票') {
			color: #FF3333;
		}

		&:contains('紧张') {
			color: #FF9900;
		}

		&:contains('有票') {
			color: #00CC66;
		}
	}

	.loading {
		padding: 40rpx;
		text-align: center;
	}

	.empty {
		padding: 80rpx 40rpx;
		text-align: center;
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 20rpx;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 40rpx;
		display: block;
	}

	.empty-btn {
		width: 200rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 28rpx;
	}

	// 筛选弹窗样式
	.filter-popup {
		background-color: #FFFFFF;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 2rpx solid #E5E5E5;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.popup-close {
		font-size: 48rpx;
		color: #999999;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.filter-content {
		padding: 20rpx 0;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.filter-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;

		&.active {
			background-color: #F0F8FF;
		}
	}

	.option-text {
		font-size: 32rpx;
		color: #333333;
	}

	.option-check {
		font-size: 32rpx;
		color: #0066CC;
		font-weight: 600;
	}

	.filter-actions {
		display: flex;
		padding: 30rpx 40rpx;
		gap: 20rpx;
		border-top: 2rpx solid #E5E5E5;
	}

	.filter-reset,
	.filter-confirm {
		flex: 1;
		height: 80rpx;
		font-size: 32rpx;
	}
</style>
