using Microsoft.EntityFrameworkCore;
using Railway.TicketSystem.Models.Entities;

namespace Railway.TicketSystem.Data.Repositories
{
    public class InventoryRepository : IInventoryRepository
    {
        private readonly RailwayDbContext _context;

        public InventoryRepository(RailwayDbContext context)
        {
            _context = context;
        }

        public async Task<List<SeatInventory>> GetInventoryAsync(int trainId, DateTime travelDate)
        {
            return await _context.SeatInventories
                .Include(si => si.SeatType)
                .Include(si => si.Train)
                .Where(si => si.TrainId == trainId && si.TravelDate.Date == travelDate.Date)
                .OrderBy(si => si.SeatType.SortOrder)
                .ToListAsync();
        }

        public async Task<SeatInventory?> GetInventoryAsync(int trainId, DateTime travelDate, int seatTypeId)
        {
            return await _context.SeatInventories
                .Include(si => si.SeatType)
                .Include(si => si.Train)
                .FirstOrDefaultAsync(si => si.TrainId == trainId 
                                         && si.TravelDate.Date == travelDate.Date 
                                         && si.SeatTypeId == seatTypeId);
        }

        public async Task<bool> UpdateInventoryAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string operation, int expectedVersion)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var inventory = await _context.SeatInventories
                    .FirstOrDefaultAsync(si => si.TrainId == trainId 
                                             && si.TravelDate.Date == travelDate.Date 
                                             && si.SeatTypeId == seatTypeId);

                if (inventory == null || inventory.Version != expectedVersion)
                {
                    return false; // 乐观锁冲突
                }

                switch (operation.ToUpper())
                {
                    case "BOOK":
                        if (inventory.AvailableSeats < quantity)
                            return false;
                        inventory.AvailableSeats -= quantity;
                        inventory.BookedSeats += quantity;
                        break;
                    case "CANCEL":
                        inventory.AvailableSeats += quantity;
                        inventory.BookedSeats -= quantity;
                        break;
                    case "LOCK":
                        if (inventory.AvailableSeats < quantity)
                            return false;
                        inventory.LockedSeats += quantity;
                        break;
                    case "UNLOCK":
                        inventory.LockedSeats -= quantity;
                        break;
                    default:
                        return false;
                }

                inventory.Version++;
                inventory.LastUpdated = DateTime.UtcNow;
                inventory.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                return false;
            }
        }

        public async Task<bool> LockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity)
        {
            var inventory = await GetInventoryAsync(trainId, travelDate, seatTypeId);
            if (inventory == null) return false;

            return await UpdateInventoryAsync(trainId, travelDate, seatTypeId, quantity, "LOCK", inventory.Version);
        }

        public async Task<bool> UnlockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity)
        {
            var inventory = await GetInventoryAsync(trainId, travelDate, seatTypeId);
            if (inventory == null) return false;

            return await UpdateInventoryAsync(trainId, travelDate, seatTypeId, quantity, "UNLOCK", inventory.Version);
        }

        public async Task<bool> BookSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity)
        {
            var inventory = await GetInventoryAsync(trainId, travelDate, seatTypeId);
            if (inventory == null) return false;

            return await UpdateInventoryAsync(trainId, travelDate, seatTypeId, quantity, "BOOK", inventory.Version);
        }

        public async Task<bool> CancelSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity)
        {
            var inventory = await GetInventoryAsync(trainId, travelDate, seatTypeId);
            if (inventory == null) return false;

            return await UpdateInventoryAsync(trainId, travelDate, seatTypeId, quantity, "CANCEL", inventory.Version);
        }

        public async Task<List<SeatInventory>> GetLowInventoryAsync(DateTime travelDate, int threshold = 10)
        {
            return await _context.SeatInventories
                .Include(si => si.Train)
                .Include(si => si.SeatType)
                .Where(si => si.TravelDate.Date == travelDate.Date && si.AvailableSeats <= threshold)
                .OrderBy(si => si.AvailableSeats)
                .ToListAsync();
        }

        public async Task<bool> InitializeInventoryAsync(int trainId, DateTime travelDate)
        {
            // 检查是否已经初始化
            var existingInventory = await _context.SeatInventories
                .AnyAsync(si => si.TrainId == trainId && si.TravelDate.Date == travelDate.Date);

            if (existingInventory) return true;

            // 获取列车的座位配置
            var seatCounts = await _context.TrainSeats
                .Where(ts => ts.TrainId == trainId && ts.IsActive)
                .GroupBy(ts => ts.SeatTypeId)
                .Select(g => new { SeatTypeId = g.Key, Count = g.Count() })
                .ToListAsync();

            var inventories = seatCounts.Select(sc => new SeatInventory
            {
                TrainId = trainId,
                TravelDate = travelDate.Date,
                SeatTypeId = sc.SeatTypeId,
                TotalSeats = sc.Count,
                AvailableSeats = sc.Count,
                BookedSeats = 0,
                LockedSeats = 0,
                Version = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                LastUpdated = DateTime.UtcNow
            }).ToList();

            _context.SeatInventories.AddRange(inventories);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<SeatInventory>> GetInventoryByDateRangeAsync(int trainId, DateTime startDate, DateTime endDate)
        {
            return await _context.SeatInventories
                .Include(si => si.SeatType)
                .Include(si => si.Train)
                .Where(si => si.TrainId == trainId 
                           && si.TravelDate.Date >= startDate.Date 
                           && si.TravelDate.Date <= endDate.Date)
                .OrderBy(si => si.TravelDate)
                .ThenBy(si => si.SeatType.SortOrder)
                .ToListAsync();
        }

        public async Task<bool> RefreshInventoryAsync(int trainId, DateTime travelDate)
        {
            // 重新计算库存（基于实际订单数据）
            var actualBookings = await _context.Orders
                .Where(o => o.TrainId == trainId 
                          && o.TravelDate.Date == travelDate.Date 
                          && o.Status == OrderStatus.Paid)
                .GroupBy(o => o.SeatTypeId)
                .Select(g => new { SeatTypeId = g.Key, BookedCount = g.Sum(o => o.Quantity) })
                .ToListAsync();

            var inventories = await GetInventoryAsync(trainId, travelDate);
            
            foreach (var inventory in inventories)
            {
                var actualBooked = actualBookings.FirstOrDefault(ab => ab.SeatTypeId == inventory.SeatTypeId)?.BookedCount ?? 0;
                inventory.BookedSeats = actualBooked;
                inventory.AvailableSeats = inventory.TotalSeats - actualBooked;
                inventory.LastUpdated = DateTime.UtcNow;
                inventory.UpdatedAt = DateTime.UtcNow;
                inventory.Version++;
            }

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
