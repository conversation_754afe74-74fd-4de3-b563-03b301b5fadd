using Microsoft.AspNetCore.Mvc;
using Railway.TicketSystem.Core.Services;
using Railway.TicketSystem.Models.DTOs;
using System.Diagnostics;

namespace Railway.TicketSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TrainController : ControllerBase
    {
        private readonly ITrainService _trainService;
        private readonly ILogger<TrainController> _logger;

        public TrainController(ITrainService trainService, ILogger<TrainController> logger)
        {
            _trainService = trainService;
            _logger = logger;
        }

        /// <summary>
        /// 查询列车信息（高并发优化版本）
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<TrainQueryResponse>> SearchTrains([FromBody] TrainQueryRequest request)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logger.LogInformation("开始查询列车: {FromStation} -> {ToStation}, 日期: {TravelDate}", 
                    request.FromStation, request.ToStation, request.TravelDate);

                var result = await _trainService.SearchTrainsAsync(request);
                
                stopwatch.Stop();
                _logger.LogInformation("查询完成，耗时: {ElapsedMs}ms, 结果数: {Count}, 缓存命中: {FromCache}", 
                    stopwatch.ElapsedMilliseconds, result.TotalCount, result.FromCache);

                return Ok(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "查询列车失败: {FromStation} -> {ToStation}", request.FromStation, request.ToStation);
                return StatusCode(500, new { message = "查询失败，请稍后重试" });
            }
        }

        /// <summary>
        /// 获取列车详细信息
        /// </summary>
        [HttpGet("{trainId}")]
        public async Task<ActionResult<TrainInfo>> GetTrainDetail(int trainId, [FromQuery] DateTime travelDate)
        {
            try
            {
                var result = await _trainService.GetTrainDetailAsync(trainId, travelDate);
                
                if (result == null)
                {
                    return NotFound(new { message = "列车信息不存在" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取列车详情失败: {TrainId}", trainId);
                return StatusCode(500, new { message = "获取列车详情失败" });
            }
        }

        /// <summary>
        /// 获取热门路线
        /// </summary>
        [HttpGet("popular-routes")]
        public async Task<ActionResult<List<string>>> GetPopularRoutes()
        {
            try
            {
                var routes = await _trainService.GetPopularRoutesAsync();
                return Ok(routes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取热门路线失败");
                return StatusCode(500, new { message = "获取热门路线失败" });
            }
        }

        /// <summary>
        /// 缓存预热
        /// </summary>
        [HttpPost("warmup")]
        public async Task<ActionResult> WarmupCache([FromQuery] DateTime? startDate, [FromQuery] int days = 7)
        {
            try
            {
                var start = startDate ?? DateTime.Today;
                await _trainService.WarmupCacheAsync(start, days);
                
                return Ok(new { message = "缓存预热完成", startDate = start, days });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缓存预热失败");
                return StatusCode(500, new { message = "缓存预热失败" });
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        [HttpGet("performance")]
        public async Task<ActionResult<PerformanceMetrics>> GetPerformanceMetrics()
        {
            try
            {
                var metrics = await _trainService.GetPerformanceMetricsAsync();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标失败");
                return StatusCode(500, new { message = "获取性能指标失败" });
            }
        }

        /// <summary>
        /// 刷新列车缓存
        /// </summary>
        [HttpPost("{trainId}/refresh-cache")]
        public async Task<ActionResult> RefreshTrainCache(int trainId)
        {
            try
            {
                await _trainService.RefreshTrainCacheAsync(trainId);
                return Ok(new { message = "缓存刷新成功", trainId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新列车缓存失败: {TrainId}", trainId);
                return StatusCode(500, new { message = "刷新缓存失败" });
            }
        }
    }
}
