/**
 * 这里是uni-app内置的常用样式变量
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #0066CC;
$uni-color-success: #00CC66;
$uni-color-warning: #FF6600;
$uni-color-error: #FF3333;

/* 文字基本颜色 */
$uni-text-color: #333333;
$uni-text-color-inverse: #FFFFFF;
$uni-text-color-grey: #999999;
$uni-text-color-placeholder: #CCCCCC;
$uni-text-color-disable: #CCCCCC;

/* 背景颜色 */
$uni-bg-color: #FFFFFF;
$uni-bg-color-grey: #F5F5F5;
$uni-bg-color-hover: #F0F8FF;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$uni-border-color: #E5E5E5;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 8rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3;

/* 文章场景相关 */
$uni-color-title: #2C405A;
$uni-color-subtitle: #555555;
$uni-color-paragraph: #3F536E;

/* 12306 专用颜色 */
$railway-primary: #0066CC;
$railway-secondary: #FF6600;
$railway-success: #00CC66;
$railway-warning: #FF9900;
$railway-danger: #FF3333;
$railway-info: #17A2B8;

/* 12306 专用背景色 */
$railway-bg-primary: #F0F8FF;
$railway-bg-secondary: #FFF8F0;
$railway-bg-light: #F8F9FA;
$railway-bg-dark: #343A40;

/* 12306 专用文字颜色 */
$railway-text-primary: #0066CC;
$railway-text-secondary: #666666;
$railway-text-muted: #999999;
$railway-text-light: #CCCCCC;

/* 12306 专用边框 */
$railway-border-light: #E5E5E5;
$railway-border-primary: #0066CC;

/* 12306 专用阴影 */
$railway-shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
$railway-shadow-base: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
$railway-shadow-lg: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);

/* 12306 专用圆角 */
$railway-radius-sm: 8rpx;
$railway-radius-base: 12rpx;
$railway-radius-lg: 16rpx;
$railway-radius-xl: 20rpx;

/* 12306 专用间距 */
$railway-space-xs: 8rpx;
$railway-space-sm: 12rpx;
$railway-space-base: 16rpx;
$railway-space-lg: 24rpx;
$railway-space-xl: 32rpx;

/* 12306 专用字体大小 */
$railway-font-xs: 20rpx;
$railway-font-sm: 24rpx;
$railway-font-base: 28rpx;
$railway-font-lg: 32rpx;
$railway-font-xl: 36rpx;
$railway-font-xxl: 40rpx;
