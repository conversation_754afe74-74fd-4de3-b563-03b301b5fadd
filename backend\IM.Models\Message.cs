using System.ComponentModel.DataAnnotations;

namespace IM.Models
{
    public class Message
    {
        public int Id { get; set; }
        
        [Required]
        public string Content { get; set; } = string.Empty;
        
        public MessageType Type { get; set; } = MessageType.Text;
        
        public int SenderId { get; set; }
        public virtual User Sender { get; set; } = null!;
        
        public int? ReceiverId { get; set; }
        public virtual User? Receiver { get; set; }
        
        public int? ChatRoomId { get; set; }
        public virtual ChatRoom? ChatRoom { get; set; }
        
        public bool IsRead { get; set; }
        
        public DateTime SentAt { get; set; }
        
        public DateTime? ReadAt { get; set; }
        
        public bool IsDeleted { get; set; }
        
        public DateTime? DeletedAt { get; set; }
        
        // For file attachments
        [StringLength(500)]
        public string? FileName { get; set; }
        
        [StringLength(500)]
        public string? FileUrl { get; set; }
        
        public long? FileSize { get; set; }
        
        [StringLength(100)]
        public string? MimeType { get; set; }
    }
    
    public enum MessageType
    {
        Text = 0,
        Image = 1,
        File = 2,
        Audio = 3,
        Video = 4,
        System = 5
    }
}
