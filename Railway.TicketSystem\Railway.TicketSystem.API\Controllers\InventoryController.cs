using Microsoft.AspNetCore.Mvc;
using Railway.TicketSystem.Core.Services;

namespace Railway.TicketSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InventoryController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<InventoryController> _logger;

        public InventoryController(IInventoryService inventoryService, ILogger<InventoryController> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
        }

        /// <summary>
        /// 获取库存信息
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<InventoryQueryResponse>> GetInventory(
            [FromQuery] int trainId, 
            [FromQuery] DateTime travelDate, 
            [FromQuery] int? seatTypeId = null)
        {
            try
            {
                var request = new InventoryQueryRequest
                {
                    TrainId = trainId,
                    TravelDate = travelDate,
                    SeatTypeId = seatTypeId
                };

                var result = await _inventoryService.GetInventoryAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存信息失败: TrainId={TrainId}, Date={TravelDate}", trainId, travelDate);
                return StatusCode(500, new { message = "获取库存信息失败" });
            }
        }

        /// <summary>
        /// 更新库存
        /// </summary>
        [HttpPost("update")]
        public async Task<ActionResult<InventoryUpdateResponse>> UpdateInventory([FromBody] InventoryUpdateRequest request)
        {
            try
            {
                var result = await _inventoryService.UpdateInventoryAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新库存失败: TrainId={TrainId}, Operation={Operation}", 
                    request.TrainId, request.Operation);
                return StatusCode(500, new { message = "更新库存失败" });
            }
        }

        /// <summary>
        /// 锁定座位
        /// </summary>
        [HttpPost("lock")]
        public async Task<ActionResult> LockSeats(
            [FromQuery] int trainId,
            [FromQuery] DateTime travelDate,
            [FromQuery] int seatTypeId,
            [FromQuery] int quantity,
            [FromQuery] string lockKey)
        {
            try
            {
                var success = await _inventoryService.LockSeatsAsync(trainId, travelDate, seatTypeId, quantity, lockKey);
                
                if (success)
                {
                    return Ok(new { message = "座位锁定成功", trainId, seatTypeId, quantity });
                }
                else
                {
                    return BadRequest(new { message = "座位锁定失败，可能库存不足" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "锁定座位失败: TrainId={TrainId}, SeatTypeId={SeatTypeId}, Quantity={Quantity}", 
                    trainId, seatTypeId, quantity);
                return StatusCode(500, new { message = "锁定座位失败" });
            }
        }

        /// <summary>
        /// 释放锁定的座位
        /// </summary>
        [HttpPost("unlock")]
        public async Task<ActionResult> UnlockSeats(
            [FromQuery] int trainId,
            [FromQuery] DateTime travelDate,
            [FromQuery] int seatTypeId,
            [FromQuery] int quantity,
            [FromQuery] string lockKey)
        {
            try
            {
                var success = await _inventoryService.UnlockSeatsAsync(trainId, travelDate, seatTypeId, quantity, lockKey);
                
                if (success)
                {
                    return Ok(new { message = "座位解锁成功", trainId, seatTypeId, quantity });
                }
                else
                {
                    return BadRequest(new { message = "座位解锁失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解锁座位失败: TrainId={TrainId}, SeatTypeId={SeatTypeId}, Quantity={Quantity}", 
                    trainId, seatTypeId, quantity);
                return StatusCode(500, new { message = "解锁座位失败" });
            }
        }

        /// <summary>
        /// 预订座位
        /// </summary>
        [HttpPost("book")]
        public async Task<ActionResult> BookSeats(
            [FromQuery] int trainId,
            [FromQuery] DateTime travelDate,
            [FromQuery] int seatTypeId,
            [FromQuery] int quantity,
            [FromQuery] string orderNumber)
        {
            try
            {
                var success = await _inventoryService.BookSeatsAsync(trainId, travelDate, seatTypeId, quantity, orderNumber);
                
                if (success)
                {
                    return Ok(new { message = "座位预订成功", trainId, seatTypeId, quantity, orderNumber });
                }
                else
                {
                    return BadRequest(new { message = "座位预订失败，可能库存不足" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预订座位失败: TrainId={TrainId}, SeatTypeId={SeatTypeId}, Quantity={Quantity}", 
                    trainId, seatTypeId, quantity);
                return StatusCode(500, new { message = "预订座位失败" });
            }
        }

        /// <summary>
        /// 取消预订
        /// </summary>
        [HttpPost("cancel")]
        public async Task<ActionResult> CancelBooking(
            [FromQuery] int trainId,
            [FromQuery] DateTime travelDate,
            [FromQuery] int seatTypeId,
            [FromQuery] int quantity,
            [FromQuery] string orderNumber)
        {
            try
            {
                var success = await _inventoryService.CancelBookingAsync(trainId, travelDate, seatTypeId, quantity, orderNumber);
                
                if (success)
                {
                    return Ok(new { message = "取消预订成功", trainId, seatTypeId, quantity, orderNumber });
                }
                else
                {
                    return BadRequest(new { message = "取消预订失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消预订失败: TrainId={TrainId}, SeatTypeId={SeatTypeId}, Quantity={Quantity}", 
                    trainId, seatTypeId, quantity);
                return StatusCode(500, new { message = "取消预订失败" });
            }
        }

        /// <summary>
        /// 初始化库存
        /// </summary>
        [HttpPost("initialize")]
        public async Task<ActionResult> InitializeInventory([FromQuery] int trainId, [FromQuery] DateTime travelDate)
        {
            try
            {
                var success = await _inventoryService.InitializeInventoryAsync(trainId, travelDate);
                
                if (success)
                {
                    return Ok(new { message = "库存初始化成功", trainId, travelDate });
                }
                else
                {
                    return BadRequest(new { message = "库存初始化失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化库存失败: TrainId={TrainId}, Date={TravelDate}", trainId, travelDate);
                return StatusCode(500, new { message = "初始化库存失败" });
            }
        }

        /// <summary>
        /// 刷新库存缓存
        /// </summary>
        [HttpPost("refresh-cache")]
        public async Task<ActionResult> RefreshInventoryCache([FromQuery] int trainId, [FromQuery] DateTime travelDate)
        {
            try
            {
                await _inventoryService.RefreshInventoryCacheAsync(trainId, travelDate);
                return Ok(new { message = "库存缓存刷新成功", trainId, travelDate });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新库存缓存失败: TrainId={TrainId}, Date={TravelDate}", trainId, travelDate);
                return StatusCode(500, new { message = "刷新库存缓存失败" });
            }
        }

        /// <summary>
        /// 获取低库存警告
        /// </summary>
        [HttpGet("low-inventory")]
        public async Task<ActionResult<List<InventoryInfo>>> GetLowInventoryWarnings(
            [FromQuery] DateTime travelDate, 
            [FromQuery] int threshold = 10)
        {
            try
            {
                var warnings = await _inventoryService.GetLowInventoryWarningsAsync(travelDate, threshold);
                return Ok(warnings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取低库存警告失败: Date={TravelDate}, Threshold={Threshold}", travelDate, threshold);
                return StatusCode(500, new { message = "获取低库存警告失败" });
            }
        }
    }
}
