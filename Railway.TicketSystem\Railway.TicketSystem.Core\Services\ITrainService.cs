using Railway.TicketSystem.Models.DTOs;

namespace Railway.TicketSystem.Core.Services
{
    public interface ITrainService
    {
        /// <summary>
        /// 查询列车信息（高并发优化版本）
        /// </summary>
        Task<TrainQueryResponse> SearchTrainsAsync(TrainQueryRequest request);
        
        /// <summary>
        /// 获取列车详细信息
        /// </summary>
        Task<TrainInfo?> GetTrainDetailAsync(int trainId, DateTime travelDate);
        
        /// <summary>
        /// 获取热门路线（用于缓存预热）
        /// </summary>
        Task<List<string>> GetPopularRoutesAsync();
        
        /// <summary>
        /// 预热缓存
        /// </summary>
        Task WarmupCacheAsync(DateTime startDate, int days = 7);
        
        /// <summary>
        /// 获取性能指标
        /// </summary>
        Task<PerformanceMetrics> GetPerformanceMetricsAsync();
        
        /// <summary>
        /// 刷新列车缓存
        /// </summary>
        Task RefreshTrainCacheAsync(int trainId);
    }
}
