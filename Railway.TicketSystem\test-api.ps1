# Railway Ticket System API 测试脚本
# PowerShell脚本用于测试API功能

$baseUrl = "http://localhost:5088/api"

Write-Host "🚄 Railway Ticket System API 测试开始" -ForegroundColor Green
Write-Host "基础URL: $baseUrl" -ForegroundColor Yellow

# 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/../health" -Method Get
    Write-Host "✅ 健康检查通过" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 初始化测试数据
Write-Host "`n2. 初始化测试数据..." -ForegroundColor Cyan
try {
    $initResponse = Invoke-RestMethod -Uri "$baseUrl/data/initialize" -Method Post
    Write-Host "✅ 测试数据初始化成功: $($initResponse.message)" -ForegroundColor Green
    Start-Sleep -Seconds 2
} catch {
    Write-Host "⚠️ 测试数据初始化失败（可能已存在）: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 获取热门路线
Write-Host "`n3. 获取热门路线..." -ForegroundColor Cyan
try {
    $routesResponse = Invoke-RestMethod -Uri "$baseUrl/train/popular-routes" -Method Get
    Write-Host "✅ 热门路线获取成功，共 $($routesResponse.Count) 条路线:" -ForegroundColor Green
    $routesResponse | ForEach-Object { Write-Host "   - $_" -ForegroundColor White }
} catch {
    Write-Host "❌ 获取热门路线失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试列车查询
Write-Host "`n4. 测试列车查询..." -ForegroundColor Cyan
$searchRequest = @{
    fromStation = "北京南"
    toStation = "上海虹桥"
    travelDate = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
    pageSize = 10
}
$searchRequestJson = $searchRequest | ConvertTo-Json

try {
    $searchResponse = Invoke-RestMethod -Uri "$baseUrl/train/search" -Method Post -Body $searchRequestJson -ContentType "application/json"
    Write-Host "✅ 列车查询成功:" -ForegroundColor Green
    Write-Host "   总数: $($searchResponse.totalCount)" -ForegroundColor White
    Write-Host "   缓存命中: $($searchResponse.fromCache)" -ForegroundColor White
    Write-Host "   响应时间: $($searchResponse.responseTime)" -ForegroundColor White
    
    if ($searchResponse.trains.Count -gt 0) {
        $firstTrain = $searchResponse.trains[0]
        Write-Host "   第一班列车: $($firstTrain.trainNumber) ($($firstTrain.departureTime) - $($firstTrain.arrivalTime))" -ForegroundColor White
    }
} catch {
    Write-Host "❌ 列车查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 再次查询测试缓存
Write-Host "`n5. 再次查询测试缓存..." -ForegroundColor Cyan
try {
    $searchResponse2 = Invoke-RestMethod -Uri "$baseUrl/train/search" -Method Post -Body $searchRequestJson -ContentType "application/json"
    Write-Host "✅ 第二次查询成功:" -ForegroundColor Green
    Write-Host "   缓存命中: $($searchResponse2.fromCache)" -ForegroundColor White
    Write-Host "   响应时间: $($searchResponse2.responseTime)" -ForegroundColor White
} catch {
    Write-Host "❌ 第二次查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试库存查询
Write-Host "`n6. 测试库存查询..." -ForegroundColor Cyan
try {
    $travelDate = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
    $inventoryResponse = Invoke-RestMethod -Uri "$baseUrl/inventory?trainId=1&travelDate=$travelDate" -Method Get
    Write-Host "✅ 库存查询成功:" -ForegroundColor Green
    Write-Host "   列车: $($inventoryResponse.trainNumber)" -ForegroundColor White
    Write-Host "   日期: $($inventoryResponse.travelDate)" -ForegroundColor White
    Write-Host "   座位类型数: $($inventoryResponse.inventories.Count)" -ForegroundColor White
    
    if ($inventoryResponse.inventories.Count -gt 0) {
        $firstSeat = $inventoryResponse.inventories[0]
        Write-Host "   $($firstSeat.seatTypeName): 总数$($firstSeat.totalSeats), 可用$($firstSeat.availableSeats)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ 库存查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试系统监控
Write-Host "`n7. 测试系统监控..." -ForegroundColor Cyan
try {
    $monitorResponse = Invoke-RestMethod -Uri "$baseUrl/monitor/overview" -Method Get
    Write-Host "✅ 系统监控获取成功:" -ForegroundColor Green
    Write-Host "   机器名: $($monitorResponse.systemInfo.machineName)" -ForegroundColor White
    Write-Host "   处理器数: $($monitorResponse.systemInfo.processorCount)" -ForegroundColor White
    Write-Host "   内存使用: $($monitorResponse.systemInfo.workingSet) MB" -ForegroundColor White
    Write-Host "   运行时间: $($monitorResponse.systemInfo.uptime)" -ForegroundColor White
    Write-Host "   缓存命中率: $($monitorResponse.cacheStatistics.overallHitRate.ToString('P2'))" -ForegroundColor White
} catch {
    Write-Host "❌ 系统监控获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试并发查询（小规模）
Write-Host "`n8. 测试并发查询（小规模）..." -ForegroundColor Cyan
try {
    $loadTestResponse = Invoke-RestMethod -Uri "$baseUrl/loadtest/concurrent-search?concurrentUsers=10&requestsPerUser=5" -Method Post
    Write-Host "✅ 并发测试完成:" -ForegroundColor Green
    Write-Host "   总请求数: $($loadTestResponse.totalRequests)" -ForegroundColor White
    Write-Host "   成功请求: $($loadTestResponse.successfulRequests)" -ForegroundColor White
    Write-Host "   失败请求: $($loadTestResponse.failedRequests)" -ForegroundColor White
    Write-Host "   缓存命中率: $($loadTestResponse.cacheHitRate.ToString('P2'))" -ForegroundColor White
    Write-Host "   平均响应时间: $($loadTestResponse.averageResponseTime)" -ForegroundColor White
    Write-Host "   QPS: $($loadTestResponse.requestsPerSecond.ToString('F2'))" -ForegroundColor White
} catch {
    Write-Host "❌ 并发测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试性能指标
Write-Host "`n9. 测试性能指标..." -ForegroundColor Cyan
try {
    $perfResponse = Invoke-RestMethod -Uri "$baseUrl/loadtest/performance-metrics" -Method Get
    Write-Host "✅ 性能指标获取成功:" -ForegroundColor Green
    Write-Host "   CPU使用率: $($perfResponse.cpuUsage.ToString('F2'))%" -ForegroundColor White
    Write-Host "   内存使用: $($perfResponse.memoryUsage) MB" -ForegroundColor White
    Write-Host "   线程数: $($perfResponse.threadCount)" -ForegroundColor White
    Write-Host "   活跃连接: $($perfResponse.activeConnections)" -ForegroundColor White
} catch {
    Write-Host "❌ 性能指标获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API测试完成！" -ForegroundColor Green
Write-Host "访问 http://localhost:5088 查看完整的Swagger文档" -ForegroundColor Yellow
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
