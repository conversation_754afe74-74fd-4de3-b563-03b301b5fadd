namespace IM.Models
{
    public class Friendship
    {
        public int Id { get; set; }
        
        public int RequesterId { get; set; }
        public virtual User Requester { get; set; } = null!;
        
        public int AddresseeId { get; set; }
        public virtual User Addressee { get; set; } = null!;
        
        public FriendshipStatus Status { get; set; } = FriendshipStatus.Pending;
        
        public DateTime RequestedAt { get; set; }
        
        public DateTime? AcceptedAt { get; set; }
        
        public DateTime? RejectedAt { get; set; }
        
        public DateTime? BlockedAt { get; set; }
        
        public string? Message { get; set; }
    }
    
    public enum FriendshipStatus
    {
        Pending = 0,    // 待确认
        Accepted = 1,   // 已接受
        Rejected = 2,   // 已拒绝
        Blocked = 3     // 已屏蔽
    }
}
