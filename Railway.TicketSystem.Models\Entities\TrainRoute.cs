using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 列车路线实体（列车经停站点）
    /// </summary>
    public class TrainRoute
    {
        public int Id { get; set; }
        
        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;
        
        public int StationId { get; set; }
        public virtual Station Station { get; set; } = null!;
        
        public int StopOrder { get; set; } // 停靠顺序
        
        public TimeSpan? ArrivalTime { get; set; } // 到达时间（起始站为null）
        
        public TimeSpan? DepartureTime { get; set; } // 出发时间（终点站为null）
        
        public int StayMinutes { get; set; } // 停留时间（分钟）
        
        public int DistanceFromStart { get; set; } // 距离起始站的里程
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
    }
}
