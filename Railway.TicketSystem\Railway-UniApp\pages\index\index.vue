<template>
	<view class="container">
		<!-- 顶部搜索卡片 -->
		<view class="search-card card">
			<view class="search-header">
				<text class="search-title">火车票</text>
				<view class="search-type">
					<text class="type-item active">单程</text>
				</view>
			</view>
			
			<!-- 出发地和目的地 -->
			<view class="station-selector">
				<view class="station-item" @click="selectStation('from')">
					<view class="station-label">出发地</view>
					<view class="station-name">{{ searchForm.fromStation || '请选择' }}</view>
				</view>
				
				<view class="exchange-btn" @click="exchangeStation">
					<text class="iconfont icon-exchange">⇄</text>
				</view>
				
				<view class="station-item" @click="selectStation('to')">
					<view class="station-label">目的地</view>
					<view class="station-name">{{ searchForm.toStation || '请选择' }}</view>
				</view>
			</view>
			
			<!-- 出发日期 -->
			<view class="date-selector" @click="selectDate">
				<view class="date-label">出发日期</view>
				<view class="date-value">
					<text class="date-text">{{ formatDate(searchForm.travelDate) }}</text>
					<text class="date-week">{{ getWeekDay(searchForm.travelDate) }}</text>
				</view>
				<text class="iconfont icon-arrow">></text>
			</view>
			
			<!-- 搜索按钮 -->
			<button class="search-btn btn-primary" @click="searchTrains" :disabled="!canSearch">
				查询车票
			</button>
		</view>
		
		<!-- 快捷功能 -->
		<view class="quick-actions card">
			<view class="action-title">快捷功能</view>
			<view class="action-grid">
				<view class="action-item" @click="goToOrderList">
					<text class="action-icon">📋</text>
					<text class="action-text">我的订单</text>
				</view>
				<view class="action-item" @click="goToProfile">
					<text class="action-icon">👤</text>
					<text class="action-text">个人中心</text>
				</view>
				<view class="action-item" @click="showSystemInfo">
					<text class="action-icon">📊</text>
					<text class="action-text">系统监控</text>
				</view>
				<view class="action-item" @click="runLoadTest">
					<text class="action-icon">⚡</text>
					<text class="action-text">压力测试</text>
				</view>
			</view>
		</view>
		
		<!-- 热门路线 -->
		<view class="popular-routes card" v-if="popularRoutes.length > 0">
			<view class="routes-title">热门路线</view>
			<view class="routes-list">
				<view 
					class="route-item" 
					v-for="(route, index) in popularRoutes" 
					:key="index"
					@click="selectPopularRoute(route)"
				>
					<text class="route-text">{{ route }}</text>
				</view>
			</view>
		</view>
		
		<!-- 车站选择弹窗 -->
		<uni-popup ref="stationPopup" type="bottom" :safe-area="false">
			<view class="station-popup">
				<view class="popup-header">
					<text class="popup-title">选择{{ stationSelectType === 'from' ? '出发地' : '目的地' }}</text>
					<text class="popup-close" @click="closeStationPopup">×</text>
				</view>
				<view class="station-search">
					<input 
						class="search-input" 
						placeholder="搜索车站" 
						v-model="stationKeyword"
						@input="searchStationList"
					/>
				</view>
				<scroll-view class="station-list" scroll-y>
					<view 
						class="station-option" 
						v-for="station in filteredStations" 
						:key="station"
						@click="selectStationOption(station)"
					>
						<text class="station-option-name">{{ station }}</text>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
		
		<!-- 日期选择弹窗 -->
		<uni-popup ref="datePopup" type="bottom" :safe-area="false">
			<view class="date-popup">
				<view class="popup-header">
					<text class="popup-title">选择出发日期</text>
					<text class="popup-close" @click="closeDatePopup">×</text>
				</view>
				<picker 
					mode="date" 
					:value="searchForm.travelDate" 
					:start="today"
					:end="maxDate"
					@change="onDateChange"
				>
					<view class="date-picker">
						<text class="date-picker-text">{{ formatDate(searchForm.travelDate) }}</text>
					</view>
				</picker>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { searchTrains, getPopularRoutes } from '@/api/train.js'
	
	export default {
		data() {
			return {
				searchForm: {
					fromStation: '',
					toStation: '',
					travelDate: ''
				},
				popularRoutes: [],
				stationSelectType: 'from', // 'from' | 'to'
				stationKeyword: '',
				allStations: [
					'北京南', '上海虹桥', '广州南', '深圳北', '杭州东',
					'南京南', '西安北', '成都东', '武汉', '长沙南',
					'天津西', '济南西', '青岛北', '沈阳北', '哈尔滨西',
					'大连北', '福州南', '厦门北', '南昌西', '合肥南'
				],
				filteredStations: [],
				today: '',
				maxDate: ''
			}
		},
		computed: {
			canSearch() {
				return this.searchForm.fromStation && 
					   this.searchForm.toStation && 
					   this.searchForm.travelDate &&
					   this.searchForm.fromStation !== this.searchForm.toStation
			}
		},
		onLoad() {
			this.initData()
			this.loadPopularRoutes()
		},
		methods: {
			initData() {
				// 初始化日期
				const today = new Date()
				this.today = this.formatDateValue(today)
				this.searchForm.travelDate = this.today
				
				// 设置最大日期（30天后）
				const maxDate = new Date()
				maxDate.setDate(maxDate.getDate() + 30)
				this.maxDate = this.formatDateValue(maxDate)
				
				// 初始化车站列表
				this.filteredStations = [...this.allStations]
			},
			
			async loadPopularRoutes() {
				try {
					this.popularRoutes = await getPopularRoutes()
				} catch (error) {
					console.error('加载热门路线失败:', error)
				}
			},
			
			selectStation(type) {
				this.stationSelectType = type
				this.stationKeyword = ''
				this.filteredStations = [...this.allStations]
				this.$refs.stationPopup.open()
			},
			
			selectStationOption(station) {
				if (this.stationSelectType === 'from') {
					this.searchForm.fromStation = station
				} else {
					this.searchForm.toStation = station
				}
				this.closeStationPopup()
			},
			
			closeStationPopup() {
				this.$refs.stationPopup.close()
			},
			
			searchStationList() {
				if (!this.stationKeyword.trim()) {
					this.filteredStations = [...this.allStations]
				} else {
					this.filteredStations = this.allStations.filter(station => 
						station.includes(this.stationKeyword.trim())
					)
				}
			},
			
			exchangeStation() {
				const temp = this.searchForm.fromStation
				this.searchForm.fromStation = this.searchForm.toStation
				this.searchForm.toStation = temp
			},
			
			selectDate() {
				this.$refs.datePopup.open()
			},
			
			onDateChange(e) {
				this.searchForm.travelDate = e.detail.value
				this.closeDatePopup()
			},
			
			closeDatePopup() {
				this.$refs.datePopup.close()
			},
			
			async searchTrains() {
				if (!this.canSearch) {
					this.$toast('请完善查询条件')
					return
				}
				
				try {
					const params = {
						fromStation: this.searchForm.fromStation,
						toStation: this.searchForm.toStation,
						travelDate: this.searchForm.travelDate,
						pageSize: 50
					}
					
					// 跳转到列车列表页
					uni.navigateTo({
						url: `/pages/train-list/train-list?params=${encodeURIComponent(JSON.stringify(params))}`
					})
				} catch (error) {
					console.error('搜索失败:', error)
					this.$toast('搜索失败，请重试')
				}
			},
			
			selectPopularRoute(route) {
				const [from, to] = route.split('-')
				this.searchForm.fromStation = from
				this.searchForm.toStation = to
			},
			
			goToOrderList() {
				uni.switchTab({
					url: '/pages/order-list/order-list'
				})
			},
			
			goToProfile() {
				uni.switchTab({
					url: '/pages/profile/profile'
				})
			},
			
			showSystemInfo() {
				uni.navigateTo({
					url: '/pages/system-info/system-info'
				})
			},
			
			runLoadTest() {
				uni.navigateTo({
					url: '/pages/load-test/load-test'
				})
			},
			
			formatDate(dateStr) {
				if (!dateStr) return ''
				const date = new Date(dateStr)
				const month = date.getMonth() + 1
				const day = date.getDate()
				return `${month}月${day}日`
			},
			
			formatDateValue(date) {
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				return `${year}-${month}-${day}`
			},
			
			getWeekDay(dateStr) {
				if (!dateStr) return ''
				const date = new Date(dateStr)
				const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
				return weekDays[date.getDay()]
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background-color: #F5F5F5;
		min-height: 100vh;
	}

	.search-card {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.search-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.search-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
	}

	.search-type {
		display: flex;
	}

	.type-item {
		padding: 12rpx 24rpx;
		font-size: 28rpx;
		color: #666666;
		border-radius: 20rpx;

		&.active {
			background-color: #0066CC;
			color: #FFFFFF;
		}
	}

	.station-selector {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
		position: relative;
	}

	.station-item {
		flex: 1;
		padding: 24rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
		border: 2rpx solid #E5E5E5;
	}

	.station-label {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
	}

	.station-name {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}

	.exchange-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 20rpx;
		background-color: #FFFFFF;
		border-radius: 50%;
		border: 2rpx solid #E5E5E5;
		font-size: 32rpx;
		color: #0066CC;
	}

	.date-selector {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
		border: 2rpx solid #E5E5E5;
		margin-bottom: 40rpx;
	}

	.date-label {
		font-size: 24rpx;
		color: #999999;
	}

	.date-value {
		flex: 1;
		margin-left: 20rpx;
	}

	.date-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		margin-right: 16rpx;
	}

	.date-week {
		font-size: 24rpx;
		color: #666666;
	}

	.icon-arrow {
		font-size: 24rpx;
		color: #999999;
	}

	.search-btn {
		width: 100%;
		height: 88rpx;
		font-size: 32rpx;
		font-weight: 600;
	}

	.quick-actions {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.action-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 30rpx;
	}

	.action-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30rpx;
	}

	.action-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
	}

	.action-icon {
		font-size: 48rpx;
		margin-bottom: 12rpx;
	}

	.action-text {
		font-size: 24rpx;
		color: #666666;
	}

	.popular-routes {
		padding: 40rpx;
	}

	.routes-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 30rpx;
	}

	.routes-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.route-item {
		padding: 16rpx 24rpx;
		background-color: #F0F8FF;
		border: 2rpx solid #0066CC;
		border-radius: 20rpx;
	}

	.route-text {
		font-size: 28rpx;
		color: #0066CC;
	}

	// 弹窗样式
	.station-popup,
	.date-popup {
		background-color: #FFFFFF;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 2rpx solid #E5E5E5;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.popup-close {
		font-size: 48rpx;
		color: #999999;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.station-search {
		padding: 30rpx 40rpx;
		border-bottom: 2rpx solid #E5E5E5;
	}

	.search-input {
		width: 100%;
		height: 80rpx;
		padding: 0 20rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
		font-size: 28rpx;
	}

	.station-list {
		height: 600rpx;
	}

	.station-option {
		padding: 30rpx 40rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}

	.station-option-name {
		font-size: 32rpx;
		color: #333333;
	}

	.date-picker {
		padding: 40rpx;
		text-align: center;
	}

	.date-picker-text {
		font-size: 36rpx;
		color: #0066CC;
		font-weight: 600;
	}
</style>
