<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="6.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Railway.TicketSystem.Core\Railway.TicketSystem.Core.csproj" />
    <ProjectReference Include="..\Railway.TicketSystem.Data\Railway.TicketSystem.Data.csproj" />
    <ProjectReference Include="..\Railway.TicketSystem.Cache\Railway.TicketSystem.Cache.csproj" />
    <ProjectReference Include="..\Railway.TicketSystem.Models\Railway.TicketSystem.Models.csproj" />
  </ItemGroup>

</Project>
