namespace IM.Models
{
    public class ChatRoomMember
    {
        public int Id { get; set; }
        
        public int ChatRoomId { get; set; }
        public virtual ChatRoom ChatRoom { get; set; } = null!;
        
        public int UserId { get; set; }
        public virtual User User { get; set; } = null!;
        
        public ChatRoomRole Role { get; set; } = ChatRoomRole.Member;
        
        public DateTime JoinedAt { get; set; }
        
        public DateTime? LeftAt { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // 最后读取消息的时间
        public DateTime? LastReadAt { get; set; }
        
        // 是否静音
        public bool IsMuted { get; set; }
    }
    
    public enum ChatRoomRole
    {
        Member = 0,
        Admin = 1,
        Owner = 2
    }
}
