using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 订单实体
    /// </summary>
    public class Order
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty; // 订单号
        
        [Required]
        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // 用户ID（简化处理，实际应该关联用户表）
        
        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;
        
        public DateTime TravelDate { get; set; } // 乘车日期
        
        public int FromStationId { get; set; }
        public virtual Station FromStation { get; set; } = null!;
        
        public int ToStationId { get; set; }
        public virtual Station ToStation { get; set; } = null!;
        
        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;
        
        public int Quantity { get; set; } // 票数
        
        public decimal UnitPrice { get; set; } // 单价
        
        public decimal TotalPrice { get; set; } // 总价
        
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        
        public DateTime? PaymentTime { get; set; } // 支付时间
        
        public DateTime? CancelTime { get; set; } // 取消时间
        
        public DateTime ExpiryTime { get; set; } // 过期时间
        
        [StringLength(500)]
        public string Remark { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
    }
    
    public enum OrderStatus
    {
        Pending = 1,     // 待支付
        Paid = 2,        // 已支付
        Cancelled = 3,   // 已取消
        Refunded = 4,    // 已退款
        Expired = 5      // 已过期
    }
}
