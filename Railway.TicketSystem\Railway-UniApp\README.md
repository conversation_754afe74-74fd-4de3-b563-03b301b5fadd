# 🚄 12306火车票订票系统 - UniApp移动端

基于UniApp开发的12306火车票订票系统移动端应用，样式参考官方12306设计。

## 📱 项目特色

### 🎨 UI设计
- **12306官方风格**：蓝色主色调（#0066CC），简洁卡片式设计
- **响应式布局**：适配不同屏幕尺寸的移动设备
- **流畅交互**：原生般的滑动、点击等手势体验
- **组件化开发**：可复用的UI组件，提高开发效率

### 🚀 核心功能
1. **车票查询**：智能搜索，支持热门路线快速选择
2. **列车列表**：详细车次信息，多维度筛选排序
3. **订单管理**：完整的订单流程，支持支付、取消等操作
4. **个人中心**：用户信息管理，快捷功能入口
5. **系统监控**：实时性能指标展示（演示功能）
6. **压力测试**：并发测试工具（演示功能）

### 🔧 技术架构
- **框架**：UniApp + Vue 3 组合式API
- **样式**：SCSS + 12306设计规范
- **状态管理**：Vuex（可选）
- **网络请求**：封装的uni.request
- **组件库**：uni-ui + 自定义组件
- **跨端支持**：H5、小程序、App

## 🏗️ 项目结构

```
Railway-UniApp/
├── api/                    # API接口封装
│   ├── request.js         # 请求工具
│   ├── train.js           # 列车相关API
│   └── inventory.js       # 库存相关API
├── components/             # 公共组件
├── pages/                  # 页面
│   ├── index/             # 首页（车票查询）
│   ├── train-list/        # 列车列表
│   ├── order-list/        # 订单列表
│   └── profile/           # 个人中心
├── static/                 # 静态资源
│   └── tabbar/            # 底部导航图标
├── App.vue                # 应用入口
├── main.js                # 入口文件
├── manifest.json          # 应用配置
├── pages.json             # 页面配置
└── uni.scss              # 全局样式变量
```

## 🎯 页面功能

### 🏠 首页 (pages/index)
- **车票查询表单**：出发地、目的地、日期选择
- **热门路线**：快速选择常用路线
- **快捷功能**：订单、个人中心、系统监控等入口
- **智能交互**：车站搜索、日期选择器

### 🚂 列车列表 (pages/train-list)
- **查询结果展示**：车次、时间、价格、余票信息
- **多维度筛选**：出发时间、车次类型
- **灵活排序**：时间排序、价格排序
- **实时状态**：有票、紧张、无票状态显示

### 📋 订单列表 (pages/order-list)
- **状态分类**：全部、待支付、已支付、已取消等
- **订单详情**：车次信息、座位类型、价格
- **操作功能**：支付、取消、查看详情
- **分页加载**：上拉加载更多

### 👤 个人中心 (pages/profile)
- **用户信息**：头像、用户名、ID显示
- **快捷功能**：订单、车票、联系人、退改签
- **系统功能**：监控、测试、设置、帮助
- **登录状态**：登录/退出功能

## 🔌 API集成

### 后端接口对接
```javascript
// 基础配置
const BASE_URL = 'http://localhost:5088/api'

// 列车查询
await searchTrains({
  fromStation: '北京南',
  toStation: '上海虹桥',
  travelDate: '2024-01-15',
  pageSize: 20
})

// 库存查询
await getInventory(trainId, travelDate, seatTypeId)
```

### 请求封装特性
- **统一错误处理**：网络异常、HTTP错误自动处理
- **加载状态管理**：自动显示/隐藏加载提示
- **请求拦截**：统一添加请求头、参数处理
- **响应拦截**：统一数据格式化、错误提示

## 🎨 设计规范

### 色彩系统
```scss
// 主色调
$railway-primary: #0066CC;      // 主蓝色
$railway-secondary: #FF6600;    // 辅助橙色
$railway-success: #00CC66;      // 成功绿色
$railway-warning: #FF9900;      // 警告黄色
$railway-danger: #FF3333;       // 危险红色

// 背景色
$railway-bg-light: #F8F9FA;     // 浅色背景
$railway-bg-primary: #F0F8FF;   // 主色背景
```

### 组件规范
- **卡片设计**：圆角16rpx，阴影效果
- **按钮样式**：主要按钮、次要按钮、文字按钮
- **文字层级**：标题、正文、辅助文字
- **间距系统**：8rpx基础单位，倍数递增

## 🚀 快速开始

### 1. 环境要求
- **HBuilderX**：3.0+
- **Node.js**：14.0+
- **微信开发者工具**：最新版（小程序开发）

### 2. 安装依赖
```bash
# 使用HBuilderX打开项目
# 或使用CLI工具
npm install
```

### 3. 配置后端地址
```javascript
// api/request.js
const BASE_URL = 'http://your-api-server:5088/api'
```

### 4. 运行项目
```bash
# H5开发
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# App开发
# 使用HBuilderX运行到手机或模拟器
```

## 📱 平台支持

### ✅ 已测试平台
- **H5**：现代浏览器，移动端浏览器
- **微信小程序**：基础功能完整
- **App**：Android/iOS原生应用

### 🔧 平台适配
- **条件编译**：针对不同平台的特殊处理
- **API兼容**：统一的接口调用方式
- **样式适配**：响应式设计，适配不同屏幕

## 🎯 演示功能

### 🔍 车票查询演示
1. 选择出发地和目的地
2. 选择出发日期
3. 查看列车列表
4. 筛选和排序功能

### 📊 系统监控演示
- 实时性能指标
- 缓存命中率统计
- 系统资源使用情况

### ⚡ 压力测试演示
- 并发查询测试
- 响应时间统计
- 成功率分析

## 🔮 扩展功能

### 待开发功能
1. **实名认证**：身份证验证
2. **支付集成**：微信支付、支付宝
3. **消息推送**：订单状态通知
4. **离线缓存**：常用数据本地存储
5. **地图集成**：车站位置显示

### 技术优化
1. **性能优化**：图片懒加载、虚拟滚动
2. **用户体验**：骨架屏、下拉刷新
3. **错误处理**：网络重试、降级方案
4. **数据同步**：实时更新、增量同步

## 📄 许可证

本项目仅用于学习和演示目的。

---

🎉 **移动端项目已完成！** 使用HBuilderX打开项目即可开始开发和调试！
