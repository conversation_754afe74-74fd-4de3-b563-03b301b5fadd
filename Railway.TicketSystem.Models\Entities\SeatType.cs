using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 座位类型实体
    /// </summary>
    public class SeatType
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty; // 商务座、一等座、二等座、硬卧、软卧等
        
        [Required]
        [StringLength(10)]
        public string TypeCode { get; set; } = string.Empty; // SW, YD, ED, YW, RW等
        
        public decimal BasePrice { get; set; } // 基础价格（每公里）
        
        public decimal PriceMultiplier { get; set; } = 1.0m; // 价格倍数
        
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;
        
        public int SortOrder { get; set; } // 排序
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<TrainSeat> TrainSeats { get; set; } = new List<TrainSeat>();
        public virtual ICollection<TicketPrice> TicketPrices { get; set; } = new List<TicketPrice>();
        public virtual ICollection<SeatInventory> SeatInventories { get; set; } = new List<SeatInventory>();
    }
}
