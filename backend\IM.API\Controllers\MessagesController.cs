using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using IM.Core.DTOs;
using IM.Core.Services;
using System.Security.Claims;

namespace IM.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MessagesController : ControllerBase
    {
        private readonly IMessageService _messageService;

        public MessagesController(IMessageService messageService)
        {
            _messageService = messageService;
        }

        [HttpPost("private")]
        public async Task<ActionResult<MessageDto>> SendPrivateMessage([FromBody] SendMessageDto messageDto)
        {
            var userId = GetCurrentUserId();
            var result = await _messageService.SendPrivateMessageAsync(userId, messageDto);
            
            if (result == null)
            {
                return BadRequest(new { message = "发送消息失败，请检查接收者是否为好友" });
            }

            return Ok(result);
        }

        [HttpPost("group")]
        public async Task<ActionResult<MessageDto>> SendGroupMessage([FromBody] SendMessageDto messageDto)
        {
            var userId = GetCurrentUserId();
            var result = await _messageService.SendGroupMessageAsync(userId, messageDto);
            
            if (result == null)
            {
                return BadRequest(new { message = "发送消息失败，请检查是否为群成员" });
            }

            return Ok(result);
        }

        [HttpGet("private/{otherUserId}")]
        public async Task<ActionResult<MessageHistoryDto>> GetPrivateMessageHistory(
            int otherUserId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 50)
        {
            var userId = GetCurrentUserId();
            var result = await _messageService.GetPrivateMessageHistoryAsync(userId, otherUserId, pageNumber, pageSize);
            
            return Ok(result);
        }

        [HttpGet("group/{chatRoomId}")]
        public async Task<ActionResult<MessageHistoryDto>> GetGroupMessageHistory(
            int chatRoomId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 50)
        {
            var userId = GetCurrentUserId();
            var result = await _messageService.GetGroupMessageHistoryAsync(userId, chatRoomId, pageNumber, pageSize);
            
            return Ok(result);
        }

        [HttpPost("{messageId}/read")]
        public async Task<ActionResult> MarkMessageAsRead(int messageId)
        {
            var userId = GetCurrentUserId();
            var success = await _messageService.MarkMessageAsReadAsync(userId, messageId);
            
            if (!success)
            {
                return BadRequest(new { message = "标记消息已读失败" });
            }

            return Ok(new { message = "消息已标记为已读" });
        }

        [HttpGet("unread")]
        public async Task<ActionResult<List<MessageDto>>> GetUnreadMessages()
        {
            var userId = GetCurrentUserId();
            var messages = await _messageService.GetUnreadMessagesAsync(userId);
            
            return Ok(messages);
        }

        [HttpDelete("{messageId}")]
        public async Task<ActionResult> DeleteMessage(int messageId)
        {
            var userId = GetCurrentUserId();
            var success = await _messageService.DeleteMessageAsync(userId, messageId);
            
            if (!success)
            {
                return BadRequest(new { message = "删除消息失败" });
            }

            return Ok(new { message = "消息已删除" });
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.Parse(userIdClaim!);
        }
    }
}
