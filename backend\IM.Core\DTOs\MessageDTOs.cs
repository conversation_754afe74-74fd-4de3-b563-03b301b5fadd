using IM.Models;
using System.ComponentModel.DataAnnotations;

namespace IM.Core.DTOs
{
    public class SendMessageDto
    {
        [Required]
        public string Content { get; set; } = string.Empty;
        
        public MessageType Type { get; set; } = MessageType.Text;
        
        public int? ReceiverId { get; set; }
        
        public int? ChatRoomId { get; set; }
        
        // For file attachments
        public string? FileName { get; set; }
        public string? FileUrl { get; set; }
        public long? FileSize { get; set; }
        public string? MimeType { get; set; }
    }
    
    public class MessageDto
    {
        public int Id { get; set; }
        public string Content { get; set; } = string.Empty;
        public MessageType Type { get; set; }
        public int SenderId { get; set; }
        public UserDto Sender { get; set; } = null!;
        public int? ReceiverId { get; set; }
        public UserDto? Receiver { get; set; }
        public int? ChatRoomId { get; set; }
        public ChatRoomDto? ChatRoom { get; set; }
        public bool IsRead { get; set; }
        public DateTime SentAt { get; set; }
        public DateTime? ReadAt { get; set; }
        public string? FileName { get; set; }
        public string? FileUrl { get; set; }
        public long? FileSize { get; set; }
        public string? MimeType { get; set; }
    }
    
    public class MessageHistoryDto
    {
        public List<MessageDto> Messages { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
    
    public class ChatRoomDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public ChatRoomType Type { get; set; }
        public int OwnerId { get; set; }
        public UserDto Owner { get; set; } = null!;
        public string? Avatar { get; set; }
        public bool IsPrivate { get; set; }
        public int MaxMembers { get; set; }
        public int MemberCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<ChatRoomMemberDto> Members { get; set; } = new();
    }
    
    public class CreateChatRoomDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public ChatRoomType Type { get; set; } = ChatRoomType.Group;
        
        public bool IsPrivate { get; set; }
        
        public int MaxMembers { get; set; } = 100;
        
        public List<int> MemberIds { get; set; } = new();
    }
    
    public class ChatRoomMemberDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public UserDto User { get; set; } = null!;
        public ChatRoomRole Role { get; set; }
        public DateTime JoinedAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastReadAt { get; set; }
        public bool IsMuted { get; set; }
    }
}
