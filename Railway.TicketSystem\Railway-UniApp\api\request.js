// API请求封装
const BASE_URL = 'http://localhost:5088/api'

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 显示加载中
    if (options.loading !== false) {
      uni.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }
    
    // 设置默认配置
    const config = {
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || 10000
    }
    
    // 发起请求
    uni.request({
      ...config,
      success: (res) => {
        // 隐藏加载中
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        // 处理响应
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          // 处理HTTP错误
          const error = {
            code: res.statusCode,
            message: `请求失败：${res.statusCode}`,
            data: res.data
          }
          
          // 显示错误提示
          if (options.showError !== false) {
            uni.showToast({
              title: error.message,
              icon: 'none',
              duration: 2000
            })
          }
          
          reject(error)
        }
      },
      fail: (err) => {
        // 隐藏加载中
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        // 处理网络错误
        const error = {
          code: -1,
          message: '网络连接失败，请检查网络设置',
          data: err
        }
        
        // 显示错误提示
        if (options.showError !== false) {
          uni.showToast({
            title: error.message,
            icon: 'none',
            duration: 2000
          })
        }
        
        reject(error)
      }
    })
  })
}

// GET请求
export const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
export const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
export const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
export const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 上传文件
export const upload = (url, filePath, options = {}) => {
  return new Promise((resolve, reject) => {
    // 显示上传进度
    if (options.loading !== false) {
      uni.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }
    
    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header: options.header || {},
      success: (res) => {
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            resolve(data)
          } catch (e) {
            resolve(res.data)
          }
        } else {
          const error = {
            code: res.statusCode,
            message: `上传失败：${res.statusCode}`,
            data: res.data
          }
          
          if (options.showError !== false) {
            uni.showToast({
              title: error.message,
              icon: 'none'
            })
          }
          
          reject(error)
        }
      },
      fail: (err) => {
        if (options.loading !== false) {
          uni.hideLoading()
        }
        
        const error = {
          code: -1,
          message: '上传失败，请检查网络连接',
          data: err
        }
        
        if (options.showError !== false) {
          uni.showToast({
            title: error.message,
            icon: 'none'
          })
        }
        
        reject(error)
      }
    })
  })
}

// 下载文件
export const download = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: BASE_URL + url,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath)
        } else {
          const error = {
            code: res.statusCode,
            message: `下载失败：${res.statusCode}`,
            data: res
          }
          
          if (options.showError !== false) {
            uni.showToast({
              title: error.message,
              icon: 'none'
            })
          }
          
          reject(error)
        }
      },
      fail: (err) => {
        const error = {
          code: -1,
          message: '下载失败，请检查网络连接',
          data: err
        }
        
        if (options.showError !== false) {
          uni.showToast({
            title: error.message,
            icon: 'none'
          })
        }
        
        reject(error)
      }
    })
  })
}

export default request
