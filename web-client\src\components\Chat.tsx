import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSignalR } from '../contexts/SignalRContext';

interface ChatMessage {
  id: number;
  content: string;
  senderId: number;
  senderName: string;
  sentAt: string;
  isOwn: boolean;
}

function Chat() {
  const { user, logout } = useAuth();
  const { connected, messages, sendPrivateMessage } = useSignalR();
  const [currentChat, setCurrentChat] = useState<number | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  useEffect(() => {
    // 转换SignalR消息为聊天消息格式
    const convertedMessages = messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      senderId: msg.senderId,
      senderName: msg.sender.displayName || msg.sender.username,
      sentAt: msg.sentAt,
      isOwn: msg.senderId === user?.id
    }));
    setChatMessages(convertedMessages);
  }, [messages, user?.id]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim() || !currentChat) return;

    try {
      await sendPrivateMessage(currentChat, messageInput.trim());
      setMessageInput('');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="app">
      <header className="header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1>IM聊天应用</h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <span>欢迎, {user?.displayName || user?.username}</span>
            <span style={{ 
              padding: '0.25rem 0.5rem', 
              borderRadius: '4px', 
              backgroundColor: connected ? '#52c41a' : '#ff4d4f',
              fontSize: '12px'
            }}>
              {connected ? '已连接' : '未连接'}
            </span>
            <button 
              onClick={logout}
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              退出登录
            </button>
          </div>
        </div>
      </header>

      <div className="main-content">
        <div className="sidebar">
          <div style={{ padding: '1rem', borderBottom: '1px solid #e8e8e8' }}>
            <h3>联系人</h3>
          </div>
          <div style={{ flex: 1, padding: '1rem' }}>
            <div 
              onClick={() => setCurrentChat(2)} // 示例：与用户ID为2的用户聊天
              style={{
                padding: '0.75rem',
                borderRadius: '4px',
                cursor: 'pointer',
                backgroundColor: currentChat === 2 ? '#e6f7ff' : 'transparent',
                border: currentChat === 2 ? '1px solid #1890ff' : '1px solid transparent'
              }}
            >
              <div style={{ fontWeight: 'bold' }}>测试用户</div>
              <div style={{ fontSize: '12px', color: '#666' }}>点击开始聊天</div>
            </div>
          </div>
        </div>

        <div className="chat-area">
          {currentChat ? (
            <>
              <div style={{ 
                padding: '1rem', 
                borderBottom: '1px solid #e8e8e8',
                backgroundColor: '#fafafa'
              }}>
                <h3>与测试用户的聊天</h3>
              </div>
              
              <div style={{ 
                flex: 1, 
                padding: '1rem', 
                overflowY: 'auto',
                display: 'flex',
                flexDirection: 'column',
                gap: '1rem'
              }}>
                {chatMessages.length === 0 ? (
                  <div style={{ 
                    textAlign: 'center', 
                    color: '#666',
                    marginTop: '2rem'
                  }}>
                    还没有消息，开始聊天吧！
                  </div>
                ) : (
                  chatMessages.map(message => (
                    <div
                      key={message.id}
                      style={{
                        display: 'flex',
                        justifyContent: message.isOwn ? 'flex-end' : 'flex-start'
                      }}
                    >
                      <div
                        style={{
                          maxWidth: '70%',
                          padding: '0.75rem',
                          borderRadius: '8px',
                          backgroundColor: message.isOwn ? '#1890ff' : '#f0f0f0',
                          color: message.isOwn ? 'white' : '#333'
                        }}
                      >
                        <div>{message.content}</div>
                        <div style={{ 
                          fontSize: '12px', 
                          opacity: 0.7,
                          marginTop: '0.25rem'
                        }}>
                          {formatTime(message.sentAt)}
                        </div>
                      </div>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              <form 
                onSubmit={handleSendMessage}
                style={{ 
                  padding: '1rem', 
                  borderTop: '1px solid #e8e8e8',
                  display: 'flex',
                  gap: '0.5rem'
                }}
              >
                <input
                  type="text"
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="输入消息..."
                  style={{
                    flex: 1,
                    padding: '0.75rem',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                  disabled={!connected}
                />
                <button
                  type="submit"
                  disabled={!connected || !messageInput.trim()}
                  style={{
                    padding: '0.75rem 1.5rem',
                    backgroundColor: '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: connected ? 'pointer' : 'not-allowed',
                    opacity: (!connected || !messageInput.trim()) ? 0.5 : 1
                  }}
                >
                  发送
                </button>
              </form>
            </>
          ) : (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center',
              height: '100%',
              color: '#666'
            }}>
              请选择一个联系人开始聊天
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Chat;
