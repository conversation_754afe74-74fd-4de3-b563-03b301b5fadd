using System.ComponentModel.DataAnnotations;

namespace IM.Models
{
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? DisplayName { get; set; }
        
        [StringLength(500)]
        public string? Avatar { get; set; }
        
        [StringLength(200)]
        public string? Status { get; set; }
        
        public bool IsOnline { get; set; }
        
        public DateTime LastSeen { get; set; }
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<Message> SentMessages { get; set; } = new List<Message>();
        public virtual ICollection<Message> ReceivedMessages { get; set; } = new List<Message>();
        public virtual ICollection<ChatRoom> OwnedChatRooms { get; set; } = new List<ChatRoom>();
        public virtual ICollection<ChatRoomMember> ChatRoomMemberships { get; set; } = new List<ChatRoomMember>();
        public virtual ICollection<Friendship> InitiatedFriendships { get; set; } = new List<Friendship>();
        public virtual ICollection<Friendship> ReceivedFriendships { get; set; } = new List<Friendship>();
    }
}
