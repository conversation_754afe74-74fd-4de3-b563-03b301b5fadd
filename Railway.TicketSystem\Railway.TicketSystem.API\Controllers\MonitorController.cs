using Microsoft.AspNetCore.Mvc;
using Railway.TicketSystem.Core.Services;
using Railway.TicketSystem.Cache;
using System.Diagnostics;
using Railway.TicketSystem.Models.DTOs;

namespace Railway.TicketSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MonitorController : ControllerBase
    {
        private readonly ITrainService _trainService;
        private readonly IInventoryService _inventoryService;
        private readonly ICacheService _cacheService;
        private readonly ILogger<MonitorController> _logger;

        public MonitorController(
            ITrainService trainService,
            IInventoryService inventoryService,
            ICacheService cacheService,
            ILogger<MonitorController> logger)
        {
            _trainService = trainService;
            _inventoryService = inventoryService;
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// 获取系统概览
        /// </summary>
        [HttpGet("overview")]
        public async Task<ActionResult<SystemOverview>> GetSystemOverview()
        {
            var process = Process.GetCurrentProcess();
            var cacheStats = await _cacheService.GetStatisticsAsync();

            var overview = new SystemOverview
            {
                Timestamp = DateTime.UtcNow,
                SystemInfo = new SystemInfo
                {
                    MachineName = Environment.MachineName,
                    ProcessorCount = Environment.ProcessorCount,
                    OSVersion = Environment.OSVersion.ToString(),
                    WorkingSet = process.WorkingSet64 / (1024 * 1024), // MB
                    ThreadCount = process.Threads.Count,
                    StartTime = process.StartTime,
                    Uptime = DateTime.Now - process.StartTime
                },
                CacheStatistics = new CacheStatisticsInfo
                {
                    L1HitRate = cacheStats.L1HitRate,
                    L2HitRate = cacheStats.L2HitRate,
                    OverallHitRate = cacheStats.OverallHitRate,
                    L1Hits = cacheStats.L1Hits,
                    L1Misses = cacheStats.L1Misses,
                    L2Hits = cacheStats.L2Hits,
                    L2Misses = cacheStats.L2Misses
                }
            };

            return Ok(overview);
        }

        /// <summary>
        /// 获取实时性能指标
        /// </summary>
        [HttpGet("performance")]
        public async Task<ActionResult<PerformanceMetrics>> GetPerformanceMetrics()
        {
            var metrics = await _trainService.GetPerformanceMetricsAsync();
            return Ok(metrics);
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        [HttpGet("cache-stats")]
        public async Task<ActionResult<CacheStatistics>> GetCacheStatistics()
        {
            var stats = await _cacheService.GetStatisticsAsync();
            return Ok(stats);
        }

        /// <summary>
        /// 获取低库存警告
        /// </summary>
        [HttpGet("low-inventory")]
        public async Task<ActionResult<List<InventoryInfo>>> GetLowInventoryWarnings(
            [FromQuery] DateTime? date = null,
            [FromQuery] int threshold = 10)
        {
            var travelDate = date ?? DateTime.Today.AddDays(1);
            var warnings = await _inventoryService.GetLowInventoryWarningsAsync(travelDate, threshold);
            return Ok(warnings);
        }

        /// <summary>
        /// 获取热门路线统计
        /// </summary>
        [HttpGet("popular-routes")]
        public async Task<ActionResult<List<RouteStatistics>>> GetPopularRoutes()
        {
            var routes = await _trainService.GetPopularRoutesAsync();
            
            // 模拟统计数据
            var statistics = routes.Select(route => new RouteStatistics
            {
                Route = route,
                SearchCount = Random.Shared.Next(100, 1000),
                BookingCount = Random.Shared.Next(50, 500),
                AverageResponseTime = TimeSpan.FromMilliseconds(Random.Shared.Next(50, 200)),
                CacheHitRate = Random.Shared.NextDouble()
            }).ToList();

            return Ok(statistics);
        }

        /// <summary>
        /// 系统健康检查
        /// </summary>
        [HttpGet("health")]
        public async Task<ActionResult<HealthStatus>> GetHealthStatus()
        {
            var healthChecks = new List<HealthCheckResult>();

            // 数据库健康检查
            try
            {
                var trains = await _trainService.GetPopularRoutesAsync();
                healthChecks.Add(new HealthCheckResult
                {
                    Name = "Database",
                    Status = "Healthy",
                    ResponseTime = TimeSpan.FromMilliseconds(Random.Shared.Next(10, 50)),
                    Message = "数据库连接正常"
                });
            }
            catch (Exception ex)
            {
                healthChecks.Add(new HealthCheckResult
                {
                    Name = "Database",
                    Status = "Unhealthy",
                    Message = ex.Message
                });
            }

            // 缓存健康检查
            try
            {
                var cacheStats = await _cacheService.GetStatisticsAsync();
                healthChecks.Add(new HealthCheckResult
                {
                    Name = "Cache",
                    Status = "Healthy",
                    ResponseTime = TimeSpan.FromMilliseconds(Random.Shared.Next(1, 10)),
                    Message = $"缓存命中率: {cacheStats.OverallHitRate:P2}"
                });
            }
            catch (Exception ex)
            {
                healthChecks.Add(new HealthCheckResult
                {
                    Name = "Cache",
                    Status = "Unhealthy",
                    Message = ex.Message
                });
            }

            var overallStatus = healthChecks.All(h => h.Status == "Healthy") ? "Healthy" : "Unhealthy";

            var healthStatus = new HealthStatus
            {
                OverallStatus = overallStatus,
                Timestamp = DateTime.UtcNow,
                Checks = healthChecks
            };

            return Ok(healthStatus);
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        [HttpPost("clear-cache")]
        public async Task<ActionResult> ClearCache([FromQuery] string? pattern = null)
        {
            try
            {
                if (string.IsNullOrEmpty(pattern))
                {
                    // 清理所有缓存的逻辑需要根据具体缓存实现
                    _logger.LogInformation("清理所有缓存");
                }
                else
                {
                    await _cacheService.RemoveByPatternAsync(pattern);
                    _logger.LogInformation("清理缓存模式: {Pattern}", pattern);
                }

                return Ok(new { message = "缓存清理完成", pattern = pattern ?? "all" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理缓存失败");
                return StatusCode(500, new { message = "清理缓存失败", error = ex.Message });
            }
        }
    }

    public class SystemOverview
    {
        public DateTime Timestamp { get; set; }
        public SystemInfo SystemInfo { get; set; } = new();
        public CacheStatisticsInfo CacheStatistics { get; set; } = new();
    }

    public class SystemInfo
    {
        public string MachineName { get; set; } = string.Empty;
        public int ProcessorCount { get; set; }
        public string OSVersion { get; set; } = string.Empty;
        public long WorkingSet { get; set; }
        public int ThreadCount { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime { get; set; }
    }

    public class CacheStatisticsInfo
    {
        public double L1HitRate { get; set; }
        public double L2HitRate { get; set; }
        public double OverallHitRate { get; set; }
        public long L1Hits { get; set; }
        public long L1Misses { get; set; }
        public long L2Hits { get; set; }
        public long L2Misses { get; set; }
    }

    public class RouteStatistics
    {
        public string Route { get; set; } = string.Empty;
        public int SearchCount { get; set; }
        public int BookingCount { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double CacheHitRate { get; set; }
    }

    public class HealthStatus
    {
        public string OverallStatus { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public List<HealthCheckResult> Checks { get; set; } = new();
    }

    public class HealthCheckResult
    {
        public string Name { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public TimeSpan ResponseTime { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
