using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 座位库存实体（按日期、车次、座位类型统计）
    /// </summary>
    public class SeatInventory
    {
        public int Id { get; set; }
        
        public int TrainId { get; set; }
        public virtual Train Train { get; set; } = null!;
        
        public DateTime TravelDate { get; set; } // 乘车日期
        
        public int SeatTypeId { get; set; }
        public virtual SeatType SeatType { get; set; } = null!;
        
        public int TotalSeats { get; set; } // 总座位数
        
        public int AvailableSeats { get; set; } // 可用座位数
        
        public int BookedSeats { get; set; } // 已预订座位数
        
        public int LockedSeats { get; set; } // 锁定座位数（临时锁定，防止超卖）
        
        public DateTime LastUpdated { get; set; }
        
        public int Version { get; set; } // 乐观锁版本号
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        // 计算属性
        public int RemainingSeats => AvailableSeats - LockedSeats;
        
        public decimal OccupancyRate => TotalSeats > 0 ? (decimal)BookedSeats / TotalSeats : 0;
    }
}
