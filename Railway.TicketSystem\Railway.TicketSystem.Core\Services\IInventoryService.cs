using Railway.TicketSystem.Models.DTOs;

namespace Railway.TicketSystem.Core.Services
{
    public interface IInventoryService
    {
        /// <summary>
        /// 获取库存信息（带缓存优化）
        /// </summary>
        Task<InventoryQueryResponse> GetInventoryAsync(InventoryQueryRequest request);
        
        /// <summary>
        /// 更新库存（防超卖）
        /// </summary>
        Task<InventoryUpdateResponse> UpdateInventoryAsync(InventoryUpdateRequest request);
        
        /// <summary>
        /// 锁定座位（临时预订）
        /// </summary>
        Task<bool> LockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string lockKey);
        
        /// <summary>
        /// 释放锁定的座位
        /// </summary>
        Task<bool> UnlockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string lockKey);
        
        /// <summary>
        /// 预订座位（确认购买）
        /// </summary>
        Task<bool> BookSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string orderNumber);
        
        /// <summary>
        /// 取消预订
        /// </summary>
        Task<bool> CancelBookingAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string orderNumber);
        
        /// <summary>
        /// 初始化库存
        /// </summary>
        Task<bool> InitializeInventoryAsync(int trainId, DateTime travelDate);
        
        /// <summary>
        /// 刷新库存缓存
        /// </summary>
        Task RefreshInventoryCacheAsync(int trainId, DateTime travelDate);
        
        /// <summary>
        /// 获取低库存警告
        /// </summary>
        Task<List<InventoryInfo>> GetLowInventoryWarningsAsync(DateTime travelDate, int threshold = 10);
    }
    
    /// <summary>
    /// 库存查询请求DTO
    /// </summary>
    public class InventoryQueryRequest
    {
        public int TrainId { get; set; }
        public DateTime TravelDate { get; set; }
        public int? SeatTypeId { get; set; } // 可选，查询特定座位类型
    }
    
    /// <summary>
    /// 库存查询响应DTO
    /// </summary>
    public class InventoryQueryResponse
    {
        public int TrainId { get; set; }
        public string TrainNumber { get; set; } = string.Empty;
        public DateTime TravelDate { get; set; }
        public List<InventoryInfo> Inventories { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public bool FromCache { get; set; }
    }
    
    /// <summary>
    /// 库存信息DTO
    /// </summary>
    public class InventoryInfo
    {
        public int SeatTypeId { get; set; }
        public string SeatTypeName { get; set; } = string.Empty;
        public string SeatTypeCode { get; set; } = string.Empty;
        public int TotalSeats { get; set; }
        public int AvailableSeats { get; set; }
        public int BookedSeats { get; set; }
        public int LockedSeats { get; set; }
        public int RemainingSeats { get; set; }
        public decimal OccupancyRate { get; set; }
        public string Status { get; set; } = string.Empty; // 充足、紧张、无票
        public DateTime LastUpdated { get; set; }
        public int Version { get; set; }
    }
    
    /// <summary>
    /// 库存更新请求DTO
    /// </summary>
    public class InventoryUpdateRequest
    {
        public int TrainId { get; set; }
        public DateTime TravelDate { get; set; }
        public int SeatTypeId { get; set; }
        public int Quantity { get; set; } // 变更数量（正数为增加，负数为减少）
        public string Operation { get; set; } = string.Empty; // BOOK, CANCEL, LOCK, UNLOCK
        public string OrderNumber { get; set; } = string.Empty; // 关联订单号
        public int ExpectedVersion { get; set; } // 期望的版本号（乐观锁）
    }
    
    /// <summary>
    /// 库存更新响应DTO
    /// </summary>
    public class InventoryUpdateResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public InventoryInfo? UpdatedInventory { get; set; }
        public int NewVersion { get; set; }
        public DateTime UpdateTime { get; set; }
    }
}
