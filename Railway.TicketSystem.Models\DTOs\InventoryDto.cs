using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.DTOs
{
    /// <summary>
    /// 库存查询请求DTO
    /// </summary>
    public class InventoryQueryRequest
    {
        [Required]
        public int TrainId { get; set; }
        
        [Required]
        public DateTime TravelDate { get; set; }
        
        public int? SeatTypeId { get; set; } // 可选，查询特定座位类型
    }
    
    /// <summary>
    /// 库存查询响应DTO
    /// </summary>
    public class InventoryQueryResponse
    {
        public int TrainId { get; set; }
        
        public string TrainNumber { get; set; } = string.Empty;
        
        public DateTime TravelDate { get; set; }
        
        public List<InventoryInfo> Inventories { get; set; } = new();
        
        public DateTime LastUpdated { get; set; }
        
        public bool FromCache { get; set; }
    }
    
    /// <summary>
    /// 库存信息DTO
    /// </summary>
    public class InventoryInfo
    {
        public int SeatTypeId { get; set; }
        
        public string SeatTypeName { get; set; } = string.Empty;
        
        public string SeatTypeCode { get; set; } = string.Empty;
        
        public int TotalSeats { get; set; }
        
        public int AvailableSeats { get; set; }
        
        public int BookedSeats { get; set; }
        
        public int LockedSeats { get; set; }
        
        public int RemainingSeats { get; set; }
        
        public decimal OccupancyRate { get; set; }
        
        public string Status { get; set; } = string.Empty; // 充足、紧张、无票
        
        public DateTime LastUpdated { get; set; }
        
        public int Version { get; set; }
    }
    
    /// <summary>
    /// 库存更新请求DTO
    /// </summary>
    public class InventoryUpdateRequest
    {
        [Required]
        public int TrainId { get; set; }
        
        [Required]
        public DateTime TravelDate { get; set; }
        
        [Required]
        public int SeatTypeId { get; set; }
        
        [Required]
        public int Quantity { get; set; } // 变更数量（正数为增加，负数为减少）
        
        public string Operation { get; set; } = string.Empty; // BOOK, CANCEL, LOCK, UNLOCK
        
        public string OrderNumber { get; set; } = string.Empty; // 关联订单号
        
        public int ExpectedVersion { get; set; } // 期望的版本号（乐观锁）
    }
    
    /// <summary>
    /// 库存更新响应DTO
    /// </summary>
    public class InventoryUpdateResponse
    {
        public bool Success { get; set; }
        
        public string Message { get; set; } = string.Empty;
        
        public InventoryInfo? UpdatedInventory { get; set; }
        
        public int NewVersion { get; set; }
        
        public DateTime UpdateTime { get; set; }
    }
}
