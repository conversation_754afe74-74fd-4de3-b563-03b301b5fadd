// 库存相关API
import { get, post } from './request.js'

// 获取库存信息
export const getInventory = (trainId, travelDate, seatTypeId = null) => {
  const params = { trainId, travelDate }
  if (seatTypeId) {
    params.seatTypeId = seatTypeId
  }
  return get('/inventory', params)
}

// 更新库存
export const updateInventory = (params) => {
  return post('/inventory/update', params)
}

// 锁定座位
export const lockSeats = (trainId, travelDate, seatTypeId, quantity, lockKey) => {
  return post('/inventory/lock', {}, {
    data: { trainId, travelDate, seatTypeId, quantity, lockKey }
  })
}

// 释放座位
export const unlockSeats = (trainId, travelDate, seatTypeId, quantity, lockKey) => {
  return post('/inventory/unlock', {}, {
    data: { trainId, travelDate, seatTypeId, quantity, lockKey }
  })
}

// 预订座位
export const bookSeats = (trainId, travelDate, seatTypeId, quantity, orderNumber) => {
  return post('/inventory/book', {}, {
    data: { trainId, travelDate, seatTypeId, quantity, orderNumber }
  })
}

// 取消预订
export const cancelBooking = (trainId, travelDate, seatTypeId, quantity, orderNumber) => {
  return post('/inventory/cancel', {}, {
    data: { trainId, travelDate, seatTypeId, quantity, orderNumber }
  })
}

// 初始化库存
export const initializeInventory = (trainId, travelDate) => {
  return post('/inventory/initialize', {}, {
    data: { trainId, travelDate }
  })
}

// 刷新库存缓存
export const refreshInventoryCache = (trainId, travelDate) => {
  return post('/inventory/refresh-cache', {}, {
    data: { trainId, travelDate },
    loading: false
  })
}

// 获取低库存警告
export const getLowInventoryWarnings = (travelDate, threshold = 10) => {
  return get('/inventory/low-inventory', { travelDate, threshold }, { loading: false })
}
