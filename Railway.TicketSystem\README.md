# 🚄 Railway Ticket System - 12306火车票订票系统演示

一个基于.NET Core 6.0的高并发火车票订票系统演示项目，重点展示高并发场景下的票务查询优化和多级缓存策略。

## 🎯 项目特色

### 🏗️ 技术架构
- **.NET Core 6.0** Web API
- **多层架构**：API层、业务逻辑层、数据访问层、缓存层
- **Entity Framework Core** + SQL Server LocalDB
- **多级缓存**：IMemoryCache (L1) + Redis (L2)
- **并发控制**：乐观锁 + 分布式锁防止超卖
- **结构化日志**：Serilog
- **API文档**：Swagger UI

### 🚀 核心功能
1. **高并发列车查询**：多级缓存优化，支持热门路线快速响应
2. **防超卖库存管理**：乐观锁版本控制 + 分布式锁
3. **性能监控**：实时缓存命中率和响应时间统计
4. **压力测试**：内置并发测试工具
5. **系统监控**：健康检查和性能指标

## 🚀 快速开始

### 1. 环境要求
- .NET 6.0 SDK
- SQL Server LocalDB（Visual Studio自带）
- 可选：Redis（用于L2缓存）

### 2. 启动项目
```bash
# 克隆项目
git clone <repository-url>
cd Railway.TicketSystem

# 构建项目
dotnet build

# 启动API服务
dotnet run --project Railway.TicketSystem.API
```

### 3. 访问应用
- **Swagger UI**: http://localhost:5088
- **健康检查**: http://localhost:5088/health

## 📊 API接口说明

### 🚂 列车查询 (`/api/train`)
- `POST /search` - 智能列车查询（带缓存优化）
- `GET /{trainId}` - 获取列车详情
- `GET /popular-routes` - 获取热门路线
- `POST /warmup` - 缓存预热
- `GET /performance` - 性能指标监控

### 📦 库存管理 (`/api/inventory`)
- `GET /` - 查询库存信息
- `POST /update` - 更新库存（防超卖）
- `POST /lock` - 锁定座位
- `POST /unlock` - 释放座位
- `POST /book` - 预订座位
- `POST /cancel` - 取消预订

### 🔧 数据管理 (`/api/data`)
- `POST /initialize` - 初始化测试数据
- `DELETE /clear` - 清空数据

### 📈 压力测试 (`/api/loadtest`)
- `POST /concurrent-search` - 并发查询压力测试
- `POST /concurrent-inventory` - 库存并发更新测试
- `GET /performance-metrics` - 系统性能指标

### 📊 系统监控 (`/api/monitor`)
- `GET /overview` - 系统概览
- `GET /performance` - 实时性能指标
- `GET /cache-stats` - 缓存统计信息
- `GET /health` - 健康检查

## 🎮 使用演示

### 1. 初始化测试数据
```bash
POST /api/data/initialize
```

### 2. 查询列车信息
```json
POST /api/train/search
{
  "fromStation": "北京南",
  "toStation": "上海虹桥",
  "travelDate": "2024-01-15",
  "pageSize": 20
}
```

### 3. 运行压力测试
```bash
POST /api/loadtest/concurrent-search?concurrentUsers=100&requestsPerUser=10
```

### 4. 查看系统监控
```bash
GET /api/monitor/overview
```

## 🔧 配置说明

### 数据库配置 (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=RailwayTicketSystem;Trusted_Connection=true;",
    "Redis": "localhost:6379"
  }
}
```

### 缓存策略
- **L1缓存（内存）**：5分钟过期，快速响应
- **L2缓存（Redis）**：30分钟过期，分布式共享
- **缓存预热**：支持热门路线预加载

## 📈 性能特性

### 高并发优化
- 多级缓存策略减少数据库压力
- 异步处理提升响应速度
- 连接池优化数据库连接

### 防超卖机制
- 乐观锁版本控制
- 分布式锁防止并发冲突
- 座位锁定机制支持临时预订

### 监控指标
- 缓存命中率统计
- 响应时间监控
- 系统资源使用情况
- 错误率和异常追踪

## 🧪 测试数据

系统预置了以下测试数据：
- **车站**：北京南、上海虹桥、广州南、深圳北等10个主要车站
- **列车**：G1、G3、G79等5趟高铁列车
- **座位类型**：商务座、一等座、二等座、硬卧、软卧
- **库存**：未来7天的座位库存数据

## 🔍 技术亮点

1. **多级缓存架构**：L1+L2缓存策略，显著提升查询性能
2. **并发安全设计**：乐观锁+分布式锁双重保护
3. **性能监控体系**：实时指标收集和分析
4. **压力测试工具**：内置并发测试验证系统性能
5. **完整的API文档**：Swagger UI提供交互式文档

## 📝 开发说明

### 项目结构
```
Railway.TicketSystem/
├── Railway.TicketSystem.API/          # Web API层
├── Railway.TicketSystem.Core/         # 业务逻辑层
├── Railway.TicketSystem.Data/         # 数据访问层
├── Railway.TicketSystem.Cache/        # 缓存层
├── Railway.TicketSystem.Models/       # 数据模型层
└── Railway.TicketSystem.Common/       # 公共组件层
```

### 扩展建议
1. 添加用户认证和授权
2. 实现订单支付流程
3. 添加消息队列处理异步任务
4. 实现微服务架构拆分
5. 添加容器化部署支持

## 📄 许可证

本项目仅用于学习和演示目的。

---

🎉 **项目已完成！** 访问 http://localhost:5088 开始体验高并发火车票订票系统！
