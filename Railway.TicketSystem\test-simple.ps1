# 简单的API测试
Write-Host "测试列车查询API..." -ForegroundColor Green

$body = @{
    fromStation = "北京南"
    toStation = "上海虹桥"
    travelDate = "2024-01-15"
    pageSize = 10
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5088/api/train/search" -Method Post -Body $body -ContentType "application/json"
    Write-Host "查询成功！" -ForegroundColor Green
    Write-Host "总数: $($response.totalCount)" -ForegroundColor Yellow
    Write-Host "缓存命中: $($response.fromCache)" -ForegroundColor Yellow
    Write-Host "响应时间: $($response.responseTime)" -ForegroundColor Yellow
    
    if ($response.trains.Count -gt 0) {
        Write-Host "第一班列车: $($response.trains[0].trainNumber)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成！" -ForegroundColor Green
