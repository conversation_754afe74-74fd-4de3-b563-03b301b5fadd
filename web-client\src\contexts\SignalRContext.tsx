import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { useAuth } from './AuthContext';

interface Message {
  id: number;
  content: string;
  type: number;
  senderId: number;
  sender: {
    id: number;
    username: string;
    displayName?: string;
    avatar?: string;
  };
  receiverId?: number;
  chatRoomId?: number;
  isRead: boolean;
  sentAt: string;
  readAt?: string;
}

interface SignalRContextType {
  connection: HubConnection | null;
  connected: boolean;
  messages: Message[];
  sendPrivateMessage: (receiverId: number, content: string) => Promise<void>;
  sendGroupMessage: (chatRoomId: number, content: string) => Promise<void>;
  joinChatRoom: (chatRoomId: number) => Promise<void>;
  leaveChatRoom: (chatRoomId: number) => Promise<void>;
}

const SignalRContext = createContext<SignalRContextType | undefined>(undefined);

export function useSignalR() {
  const context = useContext(SignalRContext);
  if (context === undefined) {
    throw new Error('useSignalR must be used within a SignalRProvider');
  }
  return context;
}

interface SignalRProviderProps {
  children: ReactNode;
}

export function SignalRProvider({ children }: SignalRProviderProps) {
  const { token } = useAuth();
  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [connected, setConnected] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    if (token) {
      const newConnection = new HubConnectionBuilder()
        .withUrl('/chathub', {
          accessTokenFactory: () => token
        })
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build();

      setConnection(newConnection);
    }

    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, [token]);

  useEffect(() => {
    if (connection) {
      connection.start()
        .then(() => {
          console.log('SignalR连接成功');
          setConnected(true);

          // 监听私聊消息
          connection.on('ReceivePrivateMessage', (message: Message) => {
            setMessages(prev => [...prev, message]);
          });

          // 监听群聊消息
          connection.on('ReceiveGroupMessage', (message: Message) => {
            setMessages(prev => [...prev, message]);
          });

          // 监听消息发送确认
          connection.on('MessageSent', (message: Message) => {
            setMessages(prev => [...prev, message]);
          });

          // 监听消息已读状态
          connection.on('MessageRead', (data: { messageId: number; readAt: string }) => {
            setMessages(prev => prev.map(msg => 
              msg.id === data.messageId 
                ? { ...msg, isRead: true, readAt: data.readAt }
                : msg
            ));
          });

          // 监听好友在线状态变化
          connection.on('FriendStatusChanged', (data: { userId: number; isOnline: boolean }) => {
            console.log('好友状态变化:', data);
          });

          // 监听打字状态
          connection.on('TypingStatus', (data: { userId: number; isTyping: boolean }) => {
            console.log('打字状态:', data);
          });
        })
        .catch(err => {
          console.error('SignalR连接失败:', err);
          setConnected(false);
        });

      connection.onclose(() => {
        setConnected(false);
        console.log('SignalR连接断开');
      });

      connection.onreconnected(() => {
        setConnected(true);
        console.log('SignalR重新连接成功');
      });
    }
  }, [connection]);

  const sendPrivateMessage = async (receiverId: number, content: string) => {
    if (connection && connected) {
      try {
        await connection.invoke('SendPrivateMessage', receiverId, content);
      } catch (error) {
        console.error('发送私聊消息失败:', error);
        throw error;
      }
    }
  };

  const sendGroupMessage = async (chatRoomId: number, content: string) => {
    if (connection && connected) {
      try {
        await connection.invoke('SendGroupMessage', chatRoomId, content);
      } catch (error) {
        console.error('发送群聊消息失败:', error);
        throw error;
      }
    }
  };

  const joinChatRoom = async (chatRoomId: number) => {
    if (connection && connected) {
      try {
        await connection.invoke('JoinChatRoom', chatRoomId);
      } catch (error) {
        console.error('加入聊天室失败:', error);
        throw error;
      }
    }
  };

  const leaveChatRoom = async (chatRoomId: number) => {
    if (connection && connected) {
      try {
        await connection.invoke('LeaveChatRoom', chatRoomId);
      } catch (error) {
        console.error('离开聊天室失败:', error);
        throw error;
      }
    }
  };

  const value = {
    connection,
    connected,
    messages,
    sendPrivateMessage,
    sendGroupMessage,
    joinChatRoom,
    leaveChatRoom
  };

  return (
    <SignalRContext.Provider value={value}>
      {children}
    </SignalRContext.Provider>
  );
}
