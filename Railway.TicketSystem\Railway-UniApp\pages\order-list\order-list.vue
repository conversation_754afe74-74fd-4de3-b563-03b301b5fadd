<template>
	<view class="container">
		<!-- 状态筛选 -->
		<view class="status-tabs">
			<view 
				v-for="(tab, index) in statusTabs" 
				:key="index"
				class="status-tab"
				:class="{ active: currentStatus === tab.value }"
				@click="switchStatus(tab.value)"
			>
				<text class="tab-text">{{ tab.label }}</text>
			</view>
		</view>
		
		<!-- 订单列表 -->
		<scroll-view class="order-list" scroll-y @scrolltolower="loadMore">
			<!-- 加载中 -->
			<view v-if="loading && orders.length === 0" class="loading">
				<uni-load-more status="loading"></uni-load-more>
			</view>
			
			<!-- 订单卡片 -->
			<view 
				v-for="order in orders" 
				:key="order.id"
				class="order-card card"
				@click="viewOrderDetail(order)"
			>
				<view class="order-header">
					<view class="order-info">
						<text class="order-number">订单号：{{ order.orderNumber }}</text>
						<text class="order-time">{{ formatDateTime(order.createdAt) }}</text>
					</view>
					<view class="order-status" :class="getStatusClass(order.status)">
						<text class="status-text">{{ getStatusText(order.status) }}</text>
					</view>
				</view>
				
				<view class="train-info">
					<view class="train-number">{{ order.trainNumber }}</view>
					<view class="train-route">
						<text class="route-text">{{ order.fromStation }} → {{ order.toStation }}</text>
						<text class="date-text">{{ formatDate(order.travelDate) }}</text>
					</view>
				</view>
				
				<view class="ticket-info">
					<view class="seat-info">
						<text class="seat-type">{{ order.seatTypeName }}</text>
						<text class="seat-count">{{ order.quantity }}张</text>
					</view>
					<view class="price-info">
						<text class="total-price">¥{{ order.totalPrice }}</text>
					</view>
				</view>
				
				<view class="order-actions" v-if="order.status === 'Pending'">
					<button class="action-btn cancel-btn" @click.stop="cancelOrder(order)">取消订单</button>
					<button class="action-btn pay-btn" @click.stop="payOrder(order)">立即支付</button>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && orders.length === 0" class="empty">
				<text class="empty-icon">📋</text>
				<text class="empty-text">暂无{{ getStatusText(currentStatus) }}订单</text>
				<button class="empty-btn btn-primary" @click="goToSearch">去订票</button>
			</view>
			
			<!-- 加载更多 -->
			<view v-if="orders.length > 0" class="load-more">
				<uni-load-more :status="loadMoreStatus" :content-text="loadMoreText"></uni-load-more>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentStatus: 'all',
				orders: [],
				loading: false,
				loadMoreStatus: 'more',
				loadMoreText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				page: 1,
				pageSize: 10,
				hasMore: true,
				
				statusTabs: [
					{ label: '全部', value: 'all' },
					{ label: '待支付', value: 'Pending' },
					{ label: '已支付', value: 'Paid' },
					{ label: '已取消', value: 'Cancelled' },
					{ label: '已退款', value: 'Refunded' }
				],
				
				// 模拟订单数据
				mockOrders: [
					{
						id: 1,
						orderNumber: 'R202401150001',
						trainNumber: 'G1',
						fromStation: '北京南',
						toStation: '上海虹桥',
						travelDate: '2024-01-15',
						seatTypeName: '二等座',
						quantity: 2,
						unitPrice: 553,
						totalPrice: 1106,
						status: 'Pending',
						createdAt: '2024-01-10T10:30:00',
						expiryTime: '2024-01-10T10:45:00'
					},
					{
						id: 2,
						orderNumber: 'R202401140002',
						trainNumber: 'G3',
						fromStation: '北京南',
						toStation: '上海虹桥',
						travelDate: '2024-01-14',
						seatTypeName: '一等座',
						quantity: 1,
						unitPrice: 933,
						totalPrice: 933,
						status: 'Paid',
						createdAt: '2024-01-09T15:20:00',
						paymentTime: '2024-01-09T15:22:00'
					},
					{
						id: 3,
						orderNumber: 'R202401130003',
						trainNumber: 'G79',
						fromStation: '北京南',
						toStation: '广州南',
						travelDate: '2024-01-13',
						seatTypeName: '二等座',
						quantity: 1,
						unitPrice: 862,
						totalPrice: 862,
						status: 'Cancelled',
						createdAt: '2024-01-08T09:15:00',
						cancelTime: '2024-01-08T09:30:00'
					}
				]
			}
		},
		onLoad() {
			this.loadOrders()
		},
		onShow() {
			// 页面显示时刷新数据
			this.refreshOrders()
		},
		methods: {
			async loadOrders() {
				if (this.loading || !this.hasMore) return
				
				this.loading = true
				this.loadMoreStatus = 'loading'
				
				try {
					// 模拟API调用
					await this.delay(1000)
					
					let filteredOrders = [...this.mockOrders]
					if (this.currentStatus !== 'all') {
						filteredOrders = filteredOrders.filter(order => order.status === this.currentStatus)
					}
					
					// 分页处理
					const start = (this.page - 1) * this.pageSize
					const end = start + this.pageSize
					const pageData = filteredOrders.slice(start, end)
					
					if (this.page === 1) {
						this.orders = pageData
					} else {
						this.orders.push(...pageData)
					}
					
					this.hasMore = end < filteredOrders.length
					this.page++
					
					this.loadMoreStatus = this.hasMore ? 'more' : 'noMore'
				} catch (error) {
					console.error('加载订单失败:', error)
					this.$toast('加载失败，请重试')
					this.loadMoreStatus = 'more'
				} finally {
					this.loading = false
				}
			},
			
			async refreshOrders() {
				this.page = 1
				this.hasMore = true
				this.orders = []
				await this.loadOrders()
			},
			
			loadMore() {
				this.loadOrders()
			},
			
			switchStatus(status) {
				if (this.currentStatus === status) return
				
				this.currentStatus = status
				this.refreshOrders()
			},
			
			viewOrderDetail(order) {
				uni.navigateTo({
					url: `/pages/order-detail/order-detail?orderId=${order.id}`
				})
			},
			
			async cancelOrder(order) {
				try {
					const confirmed = await this.$confirm('确定要取消这个订单吗？')
					if (confirmed) {
						// 模拟取消订单API
						await this.delay(500)
						
						// 更新订单状态
						const index = this.orders.findIndex(o => o.id === order.id)
						if (index > -1) {
							this.orders[index].status = 'Cancelled'
							this.orders[index].cancelTime = new Date().toISOString()
						}
						
						this.$toast('订单已取消')
					}
				} catch (error) {
					console.error('取消订单失败:', error)
					this.$toast('取消失败，请重试')
				}
			},
			
			async payOrder(order) {
				try {
					// 检查订单是否过期
					const now = new Date()
					const expiry = new Date(order.expiryTime)
					if (now > expiry) {
						this.$toast('订单已过期，请重新下单')
						return
					}
					
					// 模拟支付
					this.$loading('正在支付...')
					await this.delay(2000)
					this.$hideLoading()
					
					// 更新订单状态
					const index = this.orders.findIndex(o => o.id === order.id)
					if (index > -1) {
						this.orders[index].status = 'Paid'
						this.orders[index].paymentTime = new Date().toISOString()
					}
					
					this.$toast('支付成功')
				} catch (error) {
					this.$hideLoading()
					console.error('支付失败:', error)
					this.$toast('支付失败，请重试')
				}
			},
			
			goToSearch() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			
			getStatusText(status) {
				const statusMap = {
					'all': '全部',
					'Pending': '待支付',
					'Paid': '已支付',
					'Cancelled': '已取消',
					'Refunded': '已退款',
					'Expired': '已过期'
				}
				return statusMap[status] || status
			},
			
			getStatusClass(status) {
				return `status-${status.toLowerCase()}`
			},
			
			formatDate(dateStr) {
				const date = new Date(dateStr)
				const month = date.getMonth() + 1
				const day = date.getDate()
				const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
				const weekDay = weekDays[date.getDay()]
				return `${month}月${day}日 ${weekDay}`
			},
			
			formatDateTime(dateStr) {
				const date = new Date(dateStr)
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hour = String(date.getHours()).padStart(2, '0')
				const minute = String(date.getMinutes()).padStart(2, '0')
				return `${month}-${day} ${hour}:${minute}`
			},
			
			delay(ms) {
				return new Promise(resolve => setTimeout(resolve, ms))
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #F5F5F5;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.status-tabs {
		background-color: #FFFFFF;
		display: flex;
		padding: 0 20rpx;
		border-bottom: 2rpx solid #E5E5E5;
	}

	.status-tab {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		position: relative;

		&.active {
			.tab-text {
				color: #0066CC;
				font-weight: 600;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 4rpx;
				background-color: #0066CC;
				border-radius: 2rpx;
			}
		}
	}

	.tab-text {
		font-size: 28rpx;
		color: #666666;
	}

	.order-list {
		flex: 1;
		padding: 20rpx;
	}

	.order-card {
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 30rpx;
	}

	.order-info {
		flex: 1;
	}

	.order-number {
		display: block;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.order-time {
		font-size: 24rpx;
		color: #999999;
	}

	.order-status {
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
		font-size: 24rpx;
	}

	.status-pending {
		background-color: #FFF8E1;
		color: #FF9900;
	}

	.status-paid {
		background-color: #E8F5E8;
		color: #00CC66;
	}

	.status-cancelled {
		background-color: #FFF5F5;
		color: #FF3333;
	}

	.status-refunded {
		background-color: #F0F8FF;
		color: #0066CC;
	}

	.status-expired {
		background-color: #F5F5F5;
		color: #999999;
	}

	.train-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #F5F5F5;
	}

	.train-number {
		font-size: 36rpx;
		font-weight: 600;
		color: #0066CC;
	}

	.train-route {
		text-align: right;
	}

	.route-text {
		display: block;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.date-text {
		font-size: 24rpx;
		color: #666666;
	}

	.ticket-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.seat-info {
		flex: 1;
	}

	.seat-type {
		font-size: 28rpx;
		color: #333333;
		margin-right: 16rpx;
	}

	.seat-count {
		font-size: 24rpx;
		color: #666666;
	}

	.price-info {
		text-align: right;
	}

	.total-price {
		font-size: 36rpx;
		font-weight: 600;
		color: #FF6600;
	}

	.order-actions {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx;
		margin-top: 20rpx;
	}

	.action-btn {
		padding: 16rpx 32rpx;
		border-radius: 20rpx;
		font-size: 28rpx;
		border: none;
	}

	.cancel-btn {
		background-color: #F8F9FA;
		color: #666666;
		border: 2rpx solid #E5E5E5;
	}

	.pay-btn {
		background-color: #FF6600;
		color: #FFFFFF;
	}

	.loading {
		padding: 40rpx;
		text-align: center;
	}

	.empty {
		padding: 80rpx 40rpx;
		text-align: center;
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 20rpx;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 40rpx;
		display: block;
	}

	.empty-btn {
		width: 200rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 28rpx;
	}

	.load-more {
		padding: 20rpx;
		text-align: center;
	}
</style>
