using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using IM.Models;
using IM.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace IM.Core.Hubs
{
    [Authorize]
    public class ChatHub : Hub
    {
        private readonly IMDbContext _context;
        
        public ChatHub(IMDbContext context)
        {
            _context = context;
        }
        
        public override async Task OnConnectedAsync()
        {
            var userId = GetUserId();
            if (userId.HasValue)
            {
                // 更新用户在线状态
                var user = await _context.Users.FindAsync(userId.Value);
                if (user != null)
                {
                    user.IsOnline = true;
                    user.LastSeen = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                
                // 加入用户个人组（用于接收私聊消息）
                await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");
                
                // 通知好友用户上线
                await NotifyFriendsStatusChange(userId.Value, true);
            }
            
            await base.OnConnectedAsync();
        }
        
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = GetUserId();
            if (userId.HasValue)
            {
                // 更新用户离线状态
                var user = await _context.Users.FindAsync(userId.Value);
                if (user != null)
                {
                    user.IsOnline = false;
                    user.LastSeen = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                
                // 通知好友用户离线
                await NotifyFriendsStatusChange(userId.Value, false);
            }
            
            await base.OnDisconnectedAsync(exception);
        }
        
        // 发送私聊消息
        public async Task SendPrivateMessage(int receiverId, string content, MessageType type = MessageType.Text)
        {
            var senderId = GetUserId();
            if (!senderId.HasValue) return;
            
            // 检查是否为好友关系
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    ((f.RequesterId == senderId && f.AddresseeId == receiverId) ||
                     (f.RequesterId == receiverId && f.AddresseeId == senderId)) &&
                    f.Status == FriendshipStatus.Accepted);
            
            if (friendship == null) return;
            
            var message = new Message
            {
                SenderId = senderId.Value,
                ReceiverId = receiverId,
                Content = content,
                Type = type,
                SentAt = DateTime.UtcNow
            };
            
            _context.Messages.Add(message);
            await _context.SaveChangesAsync();
            
            // 发送给接收者
            await Clients.Group($"user_{receiverId}").SendAsync("ReceivePrivateMessage", new
            {
                Id = message.Id,
                SenderId = senderId.Value,
                Content = content,
                Type = type,
                SentAt = message.SentAt
            });
            
            // 确认发送给发送者
            await Clients.Caller.SendAsync("MessageSent", new
            {
                Id = message.Id,
                ReceiverId = receiverId,
                Content = content,
                Type = type,
                SentAt = message.SentAt
            });
        }
        
        // 发送群聊消息
        public async Task SendGroupMessage(int chatRoomId, string content, MessageType type = MessageType.Text)
        {
            var senderId = GetUserId();
            if (!senderId.HasValue) return;
            
            // 检查用户是否为群成员
            var membership = await _context.ChatRoomMembers
                .FirstOrDefaultAsync(m => m.ChatRoomId == chatRoomId && m.UserId == senderId && m.IsActive);
            
            if (membership == null) return;
            
            var message = new Message
            {
                SenderId = senderId.Value,
                ChatRoomId = chatRoomId,
                Content = content,
                Type = type,
                SentAt = DateTime.UtcNow
            };
            
            _context.Messages.Add(message);
            await _context.SaveChangesAsync();
            
            // 发送给群组所有成员
            await Clients.Group($"chatroom_{chatRoomId}").SendAsync("ReceiveGroupMessage", new
            {
                Id = message.Id,
                SenderId = senderId.Value,
                ChatRoomId = chatRoomId,
                Content = content,
                Type = type,
                SentAt = message.SentAt
            });
        }
        
        // 加入聊天室
        public async Task JoinChatRoom(int chatRoomId)
        {
            var userId = GetUserId();
            if (!userId.HasValue) return;
            
            var membership = await _context.ChatRoomMembers
                .FirstOrDefaultAsync(m => m.ChatRoomId == chatRoomId && m.UserId == userId && m.IsActive);
            
            if (membership != null)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"chatroom_{chatRoomId}");
            }
        }
        
        // 离开聊天室
        public async Task LeaveChatRoom(int chatRoomId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"chatroom_{chatRoomId}");
        }
        
        // 标记消息为已读
        public async Task MarkMessageAsRead(int messageId)
        {
            var userId = GetUserId();
            if (!userId.HasValue) return;
            
            var message = await _context.Messages.FindAsync(messageId);
            if (message != null && message.ReceiverId == userId)
            {
                message.IsRead = true;
                message.ReadAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                
                // 通知发送者消息已读
                await Clients.Group($"user_{message.SenderId}").SendAsync("MessageRead", new
                {
                    MessageId = messageId,
                    ReadAt = message.ReadAt
                });
            }
        }
        
        // 发送打字状态
        public async Task SendTypingStatus(int? receiverId, int? chatRoomId, bool isTyping)
        {
            var senderId = GetUserId();
            if (!senderId.HasValue) return;
            
            if (receiverId.HasValue)
            {
                // 私聊打字状态
                await Clients.Group($"user_{receiverId}").SendAsync("TypingStatus", new
                {
                    UserId = senderId.Value,
                    IsTyping = isTyping
                });
            }
            else if (chatRoomId.HasValue)
            {
                // 群聊打字状态
                await Clients.Group($"chatroom_{chatRoomId}").SendAsync("TypingStatus", new
                {
                    UserId = senderId.Value,
                    IsTyping = isTyping
                });
            }
        }
        
        private int? GetUserId()
        {
            var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : null;
        }
        
        private async Task NotifyFriendsStatusChange(int userId, bool isOnline)
        {
            var friendIds = await _context.Friendships
                .Where(f => (f.RequesterId == userId || f.AddresseeId == userId) && f.Status == FriendshipStatus.Accepted)
                .Select(f => f.RequesterId == userId ? f.AddresseeId : f.RequesterId)
                .ToListAsync();
            
            foreach (var friendId in friendIds)
            {
                await Clients.Group($"user_{friendId}").SendAsync("FriendStatusChanged", new
                {
                    UserId = userId,
                    IsOnline = isOnline
                });
            }
        }
    }
}
