using Railway.TicketSystem.Models.Entities;
using Railway.TicketSystem.Models.DTOs;

namespace Railway.TicketSystem.Data.Repositories
{
    public interface ITrainRepository
    {
        Task<List<Train>> SearchTrainsAsync(string fromStation, string toStation, DateTime travelDate);
        Task<Train?> GetTrainByIdAsync(int trainId);
        Task<Train?> GetTrainByNumberAsync(string trainNumber);
        Task<List<Train>> GetTrainsByRouteAsync(int fromStationId, int toStationId);
        Task<List<TrainRoute>> GetTrainRoutesAsync(int trainId);
        Task<List<TicketPrice>> GetTicketPricesAsync(int trainId, int fromStationId, int toStationId);
        Task<bool> IsValidRouteAsync(int trainId, int fromStationId, int toStationId);
        Task<List<Train>> GetActiveTrainsAsync();
        Task<List<Train>> GetTrainsByTypeAsync(string trainType);
    }
}
