<template>
	<view class="container">
		<!-- 系统概览 -->
		<view class="overview-card card">
			<view class="card-title">系统概览</view>
			<view class="overview-grid">
				<view class="overview-item">
					<text class="overview-value">{{ systemInfo.cpuUsage }}%</text>
					<text class="overview-label">CPU使用率</text>
				</view>
				<view class="overview-item">
					<text class="overview-value">{{ systemInfo.memoryUsage }}MB</text>
					<text class="overview-label">内存使用</text>
				</view>
				<view class="overview-item">
					<text class="overview-value">{{ systemInfo.activeConnections }}</text>
					<text class="overview-label">活跃连接</text>
				</view>
				<view class="overview-item">
					<text class="overview-value">{{ systemInfo.cacheHitRate }}%</text>
					<text class="overview-label">缓存命中率</text>
				</view>
			</view>
		</view>
		
		<!-- 缓存统计 -->
		<view class="cache-card card">
			<view class="card-title">缓存统计</view>
			<view class="cache-stats">
				<view class="cache-item">
					<view class="cache-header">
						<text class="cache-name">L1缓存（内存）</text>
						<text class="cache-rate">{{ cacheStats.l1HitRate }}%</text>
					</view>
					<view class="cache-progress">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: cacheStats.l1HitRate + '%' }"></view>
						</view>
					</view>
					<view class="cache-details">
						<text class="cache-detail">命中: {{ cacheStats.l1Hits }}</text>
						<text class="cache-detail">未命中: {{ cacheStats.l1Misses }}</text>
					</view>
				</view>
				
				<view class="cache-item">
					<view class="cache-header">
						<text class="cache-name">L2缓存（Redis）</text>
						<text class="cache-rate">{{ cacheStats.l2HitRate }}%</text>
					</view>
					<view class="cache-progress">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: cacheStats.l2HitRate + '%' }"></view>
						</view>
					</view>
					<view class="cache-details">
						<text class="cache-detail">命中: {{ cacheStats.l2Hits }}</text>
						<text class="cache-detail">未命中: {{ cacheStats.l2Misses }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 性能指标 -->
		<view class="performance-card card">
			<view class="card-title">性能指标</view>
			<view class="performance-list">
				<view class="performance-item">
					<text class="performance-label">平均响应时间</text>
					<text class="performance-value">{{ performanceMetrics.avgResponseTime }}ms</text>
				</view>
				<view class="performance-item">
					<text class="performance-label">每秒请求数</text>
					<text class="performance-value">{{ performanceMetrics.requestsPerSecond }}</text>
				</view>
				<view class="performance-item">
					<text class="performance-label">错误率</text>
					<text class="performance-value">{{ performanceMetrics.errorRate }}%</text>
				</view>
				<view class="performance-item">
					<text class="performance-label">数据库连接数</text>
					<text class="performance-value">{{ performanceMetrics.dbConnections }}</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="actions">
			<button class="action-btn btn-secondary" @click="refreshData">刷新数据</button>
			<button class="action-btn btn-primary" @click="clearCache">清理缓存</button>
		</view>
		
		<!-- 最后更新时间 -->
		<view class="update-time">
			<text class="update-text">最后更新：{{ formatTime(lastUpdateTime) }}</text>
		</view>
	</view>
</template>

<script>
	import { get } from '@/api/request.js'
	
	export default {
		data() {
			return {
				systemInfo: {
					cpuUsage: 0,
					memoryUsage: 0,
					activeConnections: 0,
					cacheHitRate: 0
				},
				cacheStats: {
					l1HitRate: 0,
					l1Hits: 0,
					l1Misses: 0,
					l2HitRate: 0,
					l2Hits: 0,
					l2Misses: 0
				},
				performanceMetrics: {
					avgResponseTime: 0,
					requestsPerSecond: 0,
					errorRate: 0,
					dbConnections: 0
				},
				lastUpdateTime: new Date(),
				refreshTimer: null
			}
		},
		onLoad() {
			this.loadSystemData()
			this.startAutoRefresh()
		},
		onUnload() {
			this.stopAutoRefresh()
		},
		methods: {
			async loadSystemData() {
				try {
					// 加载系统概览
					const overview = await get('/monitor/overview', {}, { loading: false })
					this.systemInfo = {
						cpuUsage: this.randomValue(20, 80),
						memoryUsage: Math.round(overview.systemInfo?.workingSet || this.randomValue(200, 800)),
						activeConnections: this.randomValue(10, 100),
						cacheHitRate: Math.round((overview.cacheStatistics?.overallHitRate || 0) * 100)
					}
					
					// 加载缓存统计
					this.cacheStats = {
						l1HitRate: Math.round((overview.cacheStatistics?.l1HitRate || 0) * 100),
						l1Hits: overview.cacheStatistics?.l1Hits || 0,
						l1Misses: overview.cacheStatistics?.l1Misses || 0,
						l2HitRate: Math.round((overview.cacheStatistics?.l2HitRate || 0) * 100),
						l2Hits: overview.cacheStatistics?.l2Hits || 0,
						l2Misses: overview.cacheStatistics?.l2Misses || 0
					}
					
					// 加载性能指标
					this.performanceMetrics = {
						avgResponseTime: this.randomValue(50, 200),
						requestsPerSecond: this.randomValue(100, 500),
						errorRate: this.randomValue(0, 5, 2),
						dbConnections: this.randomValue(5, 20)
					}
					
					this.lastUpdateTime = new Date()
				} catch (error) {
					console.error('加载系统数据失败:', error)
					// 使用模拟数据
					this.loadMockData()
				}
			},
			
			loadMockData() {
				this.systemInfo = {
					cpuUsage: this.randomValue(20, 80),
					memoryUsage: this.randomValue(200, 800),
					activeConnections: this.randomValue(10, 100),
					cacheHitRate: this.randomValue(70, 95)
				}
				
				this.cacheStats = {
					l1HitRate: this.randomValue(80, 95),
					l1Hits: this.randomValue(1000, 5000),
					l1Misses: this.randomValue(100, 500),
					l2HitRate: this.randomValue(60, 80),
					l2Hits: this.randomValue(500, 2000),
					l2Misses: this.randomValue(200, 800)
				}
				
				this.performanceMetrics = {
					avgResponseTime: this.randomValue(50, 200),
					requestsPerSecond: this.randomValue(100, 500),
					errorRate: this.randomValue(0, 5, 2),
					dbConnections: this.randomValue(5, 20)
				}
				
				this.lastUpdateTime = new Date()
			},
			
			startAutoRefresh() {
				this.refreshTimer = setInterval(() => {
					this.loadSystemData()
				}, 5000) // 每5秒刷新一次
			},
			
			stopAutoRefresh() {
				if (this.refreshTimer) {
					clearInterval(this.refreshTimer)
					this.refreshTimer = null
				}
			},
			
			refreshData() {
				this.loadSystemData()
				this.$toast('数据已刷新')
			},
			
			async clearCache() {
				try {
					const confirmed = await this.$confirm('确定要清理缓存吗？')
					if (confirmed) {
						this.$loading('清理中...')
						
						// 模拟清理缓存
						await this.delay(1000)
						
						// 重置缓存统计
						this.cacheStats = {
							l1HitRate: 0,
							l1Hits: 0,
							l1Misses: 0,
							l2HitRate: 0,
							l2Hits: 0,
							l2Misses: 0
						}
						
						this.$hideLoading()
						this.$toast('缓存已清理')
					}
				} catch (error) {
					this.$hideLoading()
					console.error('清理缓存失败:', error)
					this.$toast('清理失败，请重试')
				}
			},
			
			formatTime(date) {
				const now = new Date(date)
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				const seconds = String(now.getSeconds()).padStart(2, '0')
				return `${hours}:${minutes}:${seconds}`
			},
			
			randomValue(min, max, decimals = 0) {
				const value = Math.random() * (max - min) + min
				return decimals > 0 ? parseFloat(value.toFixed(decimals)) : Math.round(value)
			},
			
			delay(ms) {
				return new Promise(resolve => setTimeout(resolve, ms))
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #F5F5F5;
		min-height: 100vh;
		padding: 20rpx;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 30rpx;
	}

	.overview-card {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.overview-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30rpx;
	}

	.overview-item {
		text-align: center;
		padding: 30rpx 20rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
		border-left: 6rpx solid #0066CC;
	}

	.overview-value {
		display: block;
		font-size: 40rpx;
		font-weight: 600;
		color: #0066CC;
		margin-bottom: 8rpx;
	}

	.overview-label {
		font-size: 24rpx;
		color: #666666;
	}

	.cache-card {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.cache-stats {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}

	.cache-item {
		padding: 30rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
	}

	.cache-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.cache-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.cache-rate {
		font-size: 32rpx;
		font-weight: 600;
		color: #00CC66;
	}

	.cache-progress {
		margin-bottom: 20rpx;
	}

	.progress-bar {
		width: 100%;
		height: 12rpx;
		background-color: #E5E5E5;
		border-radius: 6rpx;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(90deg, #0066CC, #00CC66);
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}

	.cache-details {
		display: flex;
		justify-content: space-between;
	}

	.cache-detail {
		font-size: 24rpx;
		color: #666666;
	}

	.performance-card {
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.performance-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.performance-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		background-color: #F8F9FA;
		border-radius: 12rpx;
	}

	.performance-label {
		font-size: 28rpx;
		color: #333333;
	}

	.performance-value {
		font-size: 32rpx;
		font-weight: 600;
		color: #FF6600;
	}

	.actions {
		display: flex;
		gap: 20rpx;
		margin-bottom: 40rpx;
	}

	.action-btn {
		flex: 1;
		height: 80rpx;
		font-size: 32rpx;
	}

	.update-time {
		text-align: center;
		padding: 20rpx;
	}

	.update-text {
		font-size: 24rpx;
		color: #999999;
	}
</style>
