using Microsoft.EntityFrameworkCore;
using Railway.TicketSystem.Models.Entities;

namespace Railway.TicketSystem.Data
{
    public class RailwayDbContext : DbContext
    {
        public RailwayDbContext(DbContextOptions<RailwayDbContext> options) : base(options)
        {
        }
        
        public DbSet<Station> Stations { get; set; }
        public DbSet<Train> Trains { get; set; }
        public DbSet<SeatType> SeatTypes { get; set; }
        public DbSet<TrainRoute> TrainRoutes { get; set; }
        public DbSet<TrainSeat> TrainSeats { get; set; }
        public DbSet<TicketPrice> TicketPrices { get; set; }
        public DbSet<SeatInventory> SeatInventories { get; set; }
        public DbSet<Order> Orders { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Station configuration
            modelBuilder.Entity<Station>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.StationCode).IsUnique();
                entity.HasIndex(e => e.StationName);
                entity.HasIndex(e => new { e.CityName, e.Province });
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            });
            
            // Train configuration
            modelBuilder.Entity<Train>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.TrainNumber).IsUnique();
                entity.HasIndex(e => new { e.StartStationId, e.EndStationId });
                entity.HasIndex(e => e.TrainType);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.StartStation)
                    .WithMany()
                    .HasForeignKey(e => e.StartStationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.EndStation)
                    .WithMany()
                    .HasForeignKey(e => e.EndStationId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // SeatType configuration
            modelBuilder.Entity<SeatType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.TypeCode).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            });
            
            // TrainRoute configuration
            modelBuilder.Entity<TrainRoute>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.TrainId, e.StopOrder }).IsUnique();
                entity.HasIndex(e => new { e.TrainId, e.StationId });
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Train)
                    .WithMany(t => t.TrainRoutes)
                    .HasForeignKey(e => e.TrainId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Station)
                    .WithMany(s => s.TrainRoutes)
                    .HasForeignKey(e => e.StationId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // TrainSeat configuration
            modelBuilder.Entity<TrainSeat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.TrainId, e.CarriageNumber, e.SeatNumber }).IsUnique();
                entity.HasIndex(e => new { e.TrainId, e.SeatTypeId });
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Train)
                    .WithMany(t => t.TrainSeats)
                    .HasForeignKey(e => e.TrainId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.SeatType)
                    .WithMany(st => st.TrainSeats)
                    .HasForeignKey(e => e.SeatTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // TicketPrice configuration
            modelBuilder.Entity<TicketPrice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.TrainId, e.FromStationId, e.ToStationId, e.SeatTypeId }).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Train)
                    .WithMany(t => t.TicketPrices)
                    .HasForeignKey(e => e.TrainId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.FromStation)
                    .WithMany(s => s.FromStationPrices)
                    .HasForeignKey(e => e.FromStationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.ToStation)
                    .WithMany(s => s.ToStationPrices)
                    .HasForeignKey(e => e.ToStationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.SeatType)
                    .WithMany(st => st.TicketPrices)
                    .HasForeignKey(e => e.SeatTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // SeatInventory configuration
            modelBuilder.Entity<SeatInventory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.TrainId, e.TravelDate, e.SeatTypeId }).IsUnique();
                entity.HasIndex(e => new { e.TravelDate, e.TrainId });
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.LastUpdated).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.Version).HasDefaultValue(1);
                
                entity.HasOne(e => e.Train)
                    .WithMany(t => t.SeatInventories)
                    .HasForeignKey(e => e.TrainId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.SeatType)
                    .WithMany(st => st.SeatInventories)
                    .HasForeignKey(e => e.SeatTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Order configuration
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                entity.HasIndex(e => new { e.UserId, e.Status });
                entity.HasIndex(e => new { e.TrainId, e.TravelDate });
                entity.HasIndex(e => e.CreatedAt);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasOne(e => e.Train)
                    .WithMany(t => t.Orders)
                    .HasForeignKey(e => e.TrainId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.FromStation)
                    .WithMany()
                    .HasForeignKey(e => e.FromStationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.ToStation)
                    .WithMany()
                    .HasForeignKey(e => e.ToStationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.SeatType)
                    .WithMany()
                    .HasForeignKey(e => e.SeatTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
