using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 列车实体
    /// </summary>
    public class Train
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(10)]
        public string TrainType { get; set; } = string.Empty; // G-高铁, D-动车, T-特快, K-快速, 普通
        
        public int StartStationId { get; set; }
        public virtual Station StartStation { get; set; } = null!;
        
        public int EndStationId { get; set; }
        public virtual Station EndStation { get; set; } = null!;
        
        public TimeSpan DepartureTime { get; set; }
        
        public TimeSpan ArrivalTime { get; set; }
        
        public TimeSpan Duration { get; set; }
        
        public int TotalDistance { get; set; } // 总里程（公里）
        
        public TrainStatus Status { get; set; } = TrainStatus.Active;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<TrainRoute> TrainRoutes { get; set; } = new List<TrainRoute>();
        public virtual ICollection<TrainSeat> TrainSeats { get; set; } = new List<TrainSeat>();
        public virtual ICollection<TicketPrice> TicketPrices { get; set; } = new List<TicketPrice>();
        public virtual ICollection<SeatInventory> SeatInventories { get; set; } = new List<SeatInventory>();
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
    }
    
    public enum TrainStatus
    {
        Active = 1,     // 正常运行
        Suspended = 2,  // 暂停运行
        Cancelled = 3   // 停运
    }
}
