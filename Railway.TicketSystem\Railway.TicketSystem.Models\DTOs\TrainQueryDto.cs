using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.DTOs
{
    /// <summary>
    /// 列车查询请求DTO
    /// </summary>
    public class TrainQueryRequest
    {
        [Required]
        public string FromStation { get; set; } = string.Empty;
        
        [Required]
        public string ToStation { get; set; } = string.Empty;
        
        [Required]
        public DateTime TravelDate { get; set; }
        
        public string? TrainType { get; set; } // 列车类型过滤
        
        public TimeSpan? DepartureTimeStart { get; set; } // 出发时间范围开始
        
        public TimeSpan? DepartureTimeEnd { get; set; } // 出发时间范围结束
        
        public bool OnlyAvailable { get; set; } = true; // 只显示有票的车次
        
        public int PageNumber { get; set; } = 1;
        
        public int PageSize { get; set; } = 20;
    }
    
    /// <summary>
    /// 列车查询响应DTO
    /// </summary>
    public class TrainQueryResponse
    {
        public List<TrainInfo> Trains { get; set; } = new();
        
        public int TotalCount { get; set; }
        
        public int PageNumber { get; set; }
        
        public int PageSize { get; set; }
        
        public bool HasNextPage { get; set; }
        
        public bool HasPreviousPage { get; set; }
        
        public string QueryId { get; set; } = string.Empty; // 查询ID，用于缓存
        
        public DateTime QueryTime { get; set; }
        
        public bool FromCache { get; set; } // 是否来自缓存
        
        public TimeSpan ResponseTime { get; set; } // 响应时间
    }
    
    /// <summary>
    /// 列车信息DTO
    /// </summary>
    public class TrainInfo
    {
        public int TrainId { get; set; }
        
        public string TrainNumber { get; set; } = string.Empty;
        
        public string TrainType { get; set; } = string.Empty;
        
        public string FromStation { get; set; } = string.Empty;
        
        public string ToStation { get; set; } = string.Empty;
        
        public TimeSpan DepartureTime { get; set; }
        
        public TimeSpan ArrivalTime { get; set; }
        
        public TimeSpan Duration { get; set; }
        
        public int Distance { get; set; }
        
        public List<SeatAvailability> SeatAvailabilities { get; set; } = new();
        
        public bool HasTicket { get; set; }
        
        public DateTime LastUpdated { get; set; }
    }
    
    /// <summary>
    /// 座位可用性DTO
    /// </summary>
    public class SeatAvailability
    {
        public int SeatTypeId { get; set; }
        
        public string SeatTypeName { get; set; } = string.Empty;
        
        public string SeatTypeCode { get; set; } = string.Empty;
        
        public decimal Price { get; set; }
        
        public int TotalSeats { get; set; }
        
        public int AvailableSeats { get; set; }
        
        public int BookedSeats { get; set; }
        
        public string AvailabilityStatus { get; set; } = string.Empty; // 充足、紧张、无票
        
        public decimal OccupancyRate { get; set; }
    }
    
    /// <summary>
    /// 性能监控DTO
    /// </summary>
    public class PerformanceMetrics
    {
        public TimeSpan DatabaseQueryTime { get; set; }
        
        public TimeSpan CacheQueryTime { get; set; }
        
        public TimeSpan TotalResponseTime { get; set; }
        
        public bool CacheHit { get; set; }
        
        public string CacheLevel { get; set; } = string.Empty; // L1, L2, None
        
        public int RecordsProcessed { get; set; }
        
        public DateTime Timestamp { get; set; }
    }
}
