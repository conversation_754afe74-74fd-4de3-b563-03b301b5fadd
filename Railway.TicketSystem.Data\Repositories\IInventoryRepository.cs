using Railway.TicketSystem.Models.Entities;

namespace Railway.TicketSystem.Data.Repositories
{
    public interface IInventoryRepository
    {
        Task<List<SeatInventory>> GetInventoryAsync(int trainId, DateTime travelDate);
        Task<SeatInventory?> GetInventoryAsync(int trainId, DateTime travelDate, int seatTypeId);
        Task<bool> UpdateInventoryAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity, string operation, int expectedVersion);
        Task<bool> LockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity);
        Task<bool> UnlockSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity);
        Task<bool> BookSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity);
        Task<bool> CancelSeatsAsync(int trainId, DateTime travelDate, int seatTypeId, int quantity);
        Task<List<SeatInventory>> GetLowInventoryAsync(DateTime travelDate, int threshold = 10);
        Task<bool> InitializeInventoryAsync(int trainId, DateTime travelDate);
        Task<List<SeatInventory>> GetInventoryByDateRangeAsync(int trainId, DateTime startDate, DateTime endDate);
        Task<bool> RefreshInventoryAsync(int trainId, DateTime travelDate);
    }
}
