// 列车相关API
import { get, post } from './request.js'

// 搜索列车
export const searchTrains = (params) => {
  return post('/train/search', params)
}

// 获取列车详情
export const getTrainDetail = (trainId, travelDate) => {
  return get(`/train/${trainId}`, { travelDate })
}

// 获取热门路线
export const getPopularRoutes = () => {
  return get('/train/popular-routes')
}

// 缓存预热
export const warmupCache = (startDate, days = 7) => {
  return post('/train/warmup', {}, { 
    data: { startDate, days },
    loading: false 
  })
}

// 获取性能指标
export const getPerformanceMetrics = () => {
  return get('/train/performance', {}, { loading: false })
}

// 刷新列车缓存
export const refreshTrainCache = (trainId) => {
  return post(`/train/${trainId}/refresh-cache`, {}, { loading: false })
}

// 获取所有车站
export const getAllStations = () => {
  return get('/train/stations', {}, { loading: false })
}

// 根据关键词搜索车站
export const searchStations = (keyword) => {
  return get('/train/stations/search', { keyword }, { loading: false })
}
