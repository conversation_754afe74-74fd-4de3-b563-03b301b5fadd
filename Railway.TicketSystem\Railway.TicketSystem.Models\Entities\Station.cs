using System.ComponentModel.DataAnnotations;

namespace Railway.TicketSystem.Models.Entities
{
    /// <summary>
    /// 车站实体
    /// </summary>
    public class Station
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string StationName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(10)]
        public string StationCode { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string CityName { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string Province { get; set; } = string.Empty;
        
        public decimal Longitude { get; set; }
        
        public decimal Latitude { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<TrainRoute> TrainRoutes { get; set; } = new List<TrainRoute>();
        public virtual ICollection<TicketPrice> FromStationPrices { get; set; } = new List<TicketPrice>();
        public virtual ICollection<TicketPrice> ToStationPrices { get; set; } = new List<TicketPrice>();
    }
}
