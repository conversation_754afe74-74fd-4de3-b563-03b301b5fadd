namespace Railway.TicketSystem.Cache
{
    public interface ICacheService
    {
        // 基础缓存操作
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task RemoveAsync(string key);
        Task RemoveByPatternAsync(string pattern);
        Task<bool> ExistsAsync(string key);
        
        // 多级缓存操作
        Task<T?> GetFromL1CacheAsync<T>(string key) where T : class;
        Task<T?> GetFromL2CacheAsync<T>(string key) where T : class;
        Task SetL1CacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task SetL2CacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        
        // 缓存统计
        Task<CacheStatistics> GetStatisticsAsync();
        
        // 缓存预热
        Task WarmupAsync();
        
        // 分布式锁
        Task<IDistributedLock> AcquireLockAsync(string key, TimeSpan expiration);
    }
    
    public interface IDistributedLock : IDisposable
    {
        string Key { get; }
        bool IsAcquired { get; }
        Task<bool> ExtendAsync(TimeSpan expiration);
        Task ReleaseAsync();
    }
    
    public class CacheStatistics
    {
        public long L1Hits { get; set; }
        public long L1Misses { get; set; }
        public long L2Hits { get; set; }
        public long L2Misses { get; set; }
        public double L1HitRate => L1Hits + L1Misses > 0 ? (double)L1Hits / (L1Hits + L1Misses) : 0;
        public double L2HitRate => L2Hits + L2Misses > 0 ? (double)L2Hits / (L2Hits + L2Misses) : 0;
        public double OverallHitRate => (L1Hits + L2Hits + L1Misses + L2Misses) > 0 ? 
            (double)(L1Hits + L2Hits) / (L1Hits + L2Hits + L1Misses + L2Misses) : 0;
    }
}
