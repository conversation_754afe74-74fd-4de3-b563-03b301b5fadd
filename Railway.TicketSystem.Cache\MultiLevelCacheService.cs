using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Collections.Concurrent;

namespace Railway.TicketSystem.Cache
{
    public class MultiLevelCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<MultiLevelCacheService> _logger;
        private readonly CacheStatistics _statistics;
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _lockSemaphores;
        
        private readonly TimeSpan _defaultL1Expiration = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _defaultL2Expiration = TimeSpan.FromMinutes(30);

        public MultiLevelCacheService(
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            ILogger<MultiLevelCacheService> logger)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
            _statistics = new CacheStatistics();
            _lockSemaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            // 先尝试从L1缓存获取
            var result = await GetFromL1CacheAsync<T>(key);
            if (result != null)
            {
                Interlocked.Increment(ref _statistics.L1Hits);
                return result;
            }
            
            Interlocked.Increment(ref _statistics.L1Misses);

            // 再尝试从L2缓存获取
            result = await GetFromL2CacheAsync<T>(key);
            if (result != null)
            {
                Interlocked.Increment(ref _statistics.L2Hits);
                
                // 回写到L1缓存
                await SetL1CacheAsync(key, result, _defaultL1Expiration);
                return result;
            }
            
            Interlocked.Increment(ref _statistics.L2Misses);
            return null;
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            var l1Expiration = expiration ?? _defaultL1Expiration;
            var l2Expiration = expiration ?? _defaultL2Expiration;
            
            // 同时设置L1和L2缓存
            await SetL1CacheAsync(key, value, l1Expiration);
            await SetL2CacheAsync(key, value, l2Expiration);
        }

        public async Task<T?> GetFromL1CacheAsync<T>(string key) where T : class
        {
            try
            {
                return _memoryCache.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting from L1 cache with key: {Key}", key);
                return null;
            }
        }

        public async Task<T?> GetFromL2CacheAsync<T>(string key) where T : class
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                if (string.IsNullOrEmpty(cachedValue))
                    return null;

                return JsonSerializer.Deserialize<T>(cachedValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting from L2 cache with key: {Key}", key);
                return null;
            }
        }

        public async Task SetL1CacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? _defaultL1Expiration,
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, value, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting L1 cache with key: {Key}", key);
            }
        }

        public async Task SetL2CacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var serializedValue = JsonSerializer.Serialize(value);
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? _defaultL2Expiration
                };

                await _distributedCache.SetStringAsync(key, serializedValue, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting L2 cache with key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                await _distributedCache.RemoveAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache with key: {Key}", key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            // 注意：这个实现比较简单，实际生产环境中可能需要更复杂的模式匹配
            // 对于Redis，可以使用SCAN命令来实现
            _logger.LogWarning("RemoveByPatternAsync not fully implemented for pattern: {Pattern}", pattern);
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out _))
                    return true;

                var value = await _distributedCache.GetStringAsync(key);
                return !string.IsNullOrEmpty(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence with key: {Key}", key);
                return false;
            }
        }

        public async Task<CacheStatistics> GetStatisticsAsync()
        {
            return new CacheStatistics
            {
                L1Hits = _statistics.L1Hits,
                L1Misses = _statistics.L1Misses,
                L2Hits = _statistics.L2Hits,
                L2Misses = _statistics.L2Misses
            };
        }

        public async Task WarmupAsync()
        {
            _logger.LogInformation("Starting cache warmup...");
            
            // 这里可以实现缓存预热逻辑
            // 例如：预加载热门路线、常用车站等数据
            
            _logger.LogInformation("Cache warmup completed");
        }

        public async Task<IDistributedLock> AcquireLockAsync(string key, TimeSpan expiration)
        {
            return new RedisDistributedLock(_distributedCache, key, expiration, _logger);
        }
    }

    public class RedisDistributedLock : IDistributedLock
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger _logger;
        private readonly string _lockKey;
        private readonly string _lockValue;
        private readonly TimeSpan _expiration;
        private bool _disposed = false;

        public string Key { get; }
        public bool IsAcquired { get; private set; }

        public RedisDistributedLock(IDistributedCache distributedCache, string key, TimeSpan expiration, ILogger logger)
        {
            _distributedCache = distributedCache;
            _logger = logger;
            Key = key;
            _lockKey = $"lock:{key}";
            _lockValue = Guid.NewGuid().ToString();
            _expiration = expiration;
            
            // 尝试获取锁
            TryAcquireLock();
        }

        private void TryAcquireLock()
        {
            try
            {
                // 简化的分布式锁实现
                // 实际生产环境中应该使用更可靠的Redis Lua脚本实现
                var existingValue = _distributedCache.GetString(_lockKey);
                if (string.IsNullOrEmpty(existingValue))
                {
                    var options = new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = _expiration
                    };
                    _distributedCache.SetString(_lockKey, _lockValue, options);
                    IsAcquired = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acquiring distributed lock: {Key}", Key);
                IsAcquired = false;
            }
        }

        public async Task<bool> ExtendAsync(TimeSpan expiration)
        {
            if (!IsAcquired) return false;

            try
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration
                };
                await _distributedCache.SetStringAsync(_lockKey, _lockValue, options);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extending distributed lock: {Key}", Key);
                return false;
            }
        }

        public async Task ReleaseAsync()
        {
            if (!IsAcquired) return;

            try
            {
                await _distributedCache.RemoveAsync(_lockKey);
                IsAcquired = false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error releasing distributed lock: {Key}", Key);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                ReleaseAsync().Wait();
                _disposed = true;
            }
        }
    }
}
